"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/api/phrases/random/route";
exports.ids = ["app/api/phrases/random/route"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-route.runtime.dev.js":
/*!**************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-route.runtime.dev.js" ***!
  \**************************************************************************/
/***/ ((module) => {

module.exports = require("next/dist/compiled/next-server/app-route.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fphrases%2Frandom%2Froute&page=%2Fapi%2Fphrases%2Frandom%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphrases%2Frandom%2Froute.ts&appDir=D%3A%5CSoftware%20Projects%5CLanguage%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSoftware%20Projects%5CLanguage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fphrases%2Frandom%2Froute&page=%2Fapi%2Fphrases%2Frandom%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphrases%2Frandom%2Froute.ts&appDir=D%3A%5CSoftware%20Projects%5CLanguage%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSoftware%20Projects%5CLanguage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \******************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   patchFetch: () => (/* binding */ patchFetch),\n/* harmony export */   requestAsyncStorage: () => (/* binding */ requestAsyncStorage),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   serverHooks: () => (/* binding */ serverHooks),\n/* harmony export */   staticGenerationAsyncStorage: () => (/* binding */ staticGenerationAsyncStorage)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-route/module.compiled */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-route/module.compiled.js\");\n/* harmony import */ var next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/server/lib/patch-fetch */ \"(rsc)/./node_modules/next/dist/server/lib/patch-fetch.js\");\n/* harmony import */ var next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var D_Software_Projects_Language_app_api_phrases_random_route_ts__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./app/api/phrases/random/route.ts */ \"(rsc)/./app/api/phrases/random/route.ts\");\n\n\n\n\n// We inject the nextConfigOutput here so that we can use them in the route\n// module.\nconst nextConfigOutput = \"\"\nconst routeModule = new next_dist_server_future_route_modules_app_route_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppRouteRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_ROUTE,\n        page: \"/api/phrases/random/route\",\n        pathname: \"/api/phrases/random\",\n        filename: \"route\",\n        bundlePath: \"app/api/phrases/random/route\"\n    },\n    resolvedPagePath: \"D:\\\\Software Projects\\\\Language\\\\app\\\\api\\\\phrases\\\\random\\\\route.ts\",\n    nextConfigOutput,\n    userland: D_Software_Projects_Language_app_api_phrases_random_route_ts__WEBPACK_IMPORTED_MODULE_3__\n});\n// Pull out the exports that we need to expose from the module. This should\n// be eliminated when we've moved the other routes to the new format. These\n// are used to hook into the route.\nconst { requestAsyncStorage, staticGenerationAsyncStorage, serverHooks } = routeModule;\nconst originalPathname = \"/api/phrases/random/route\";\nfunction patchFetch() {\n    return (0,next_dist_server_lib_patch_fetch__WEBPACK_IMPORTED_MODULE_2__.patchFetch)({\n        serverHooks,\n        staticGenerationAsyncStorage\n    });\n}\n\n\n//# sourceMappingURL=app-route.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fphrases%2Frandom%2Froute&page=%2Fapi%2Fphrases%2Frandom%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphrases%2Frandom%2Froute.ts&appDir=D%3A%5CSoftware%20Projects%5CLanguage%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSoftware%20Projects%5CLanguage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst authOptions = {\n    // Temporarily disable database adapter to test OAuth flow\n    // adapter: PrismaAdapter(prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, token }) {\n            if (session.user && token?.sub) {\n                session.user.id = token.sub;\n                // Include preferred language from token\n                session.user.preferredLanguage = token.preferredLanguage;\n            }\n            return session;\n        },\n        async jwt ({ token, account, profile, trigger, session }) {\n            if (account) {\n                token.accessToken = account.access_token;\n                // When user first signs in, fetch their language preference from database\n                try {\n                    const prismaClient = new _prisma_client__WEBPACK_IMPORTED_MODULE_2__.PrismaClient();\n                    let user = await prismaClient.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            preferredLanguage: true\n                        }\n                    });\n                    // If user doesn't exist, create them with default language preference\n                    if (!user) {\n                        user = await prismaClient.user.create({\n                            data: {\n                                id: token.sub,\n                                name: token.name,\n                                email: token.email,\n                                image: token.picture,\n                                preferredLanguage: \"am\" // Default to Amharic\n                            },\n                            select: {\n                                preferredLanguage: true\n                            }\n                        });\n                        console.log(`Created new user with default language preference: ${token.sub}`);\n                    }\n                    token.preferredLanguage = user.preferredLanguage;\n                    console.log(`Loaded language preference for user ${token.sub}: ${user.preferredLanguage}`);\n                    await prismaClient.$disconnect();\n                } catch (error) {\n                    console.error(\"Error fetching user language preference:\", error);\n                    token.preferredLanguage = \"am\"; // Default fallback\n                }\n            }\n            // Handle session updates (like language preference changes)\n            if (trigger === \"update\" && session?.user?.preferredLanguage) {\n                token.preferredLanguage = session.user.preferredLanguage;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\"\n    }\n};\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/api/phrases/random/route.ts":
/*!*****************************************!*\
  !*** ./app/api/phrases/random/route.ts ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ GET)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(rsc)/./node_modules/next/dist/api/server.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../../auth/[...nextauth]/route */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _lib_wordnik__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/wordnik */ \"(rsc)/./lib/wordnik.ts\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_4__);\n\n\n\n\n\n// Pre-defined translations for common English idioms\nfunction getPhraseTranslations(phrase) {\n    const translations = {\n        \"break the ice\": {\n            \"es\": \"romper el hielo\",\n            \"fr\": \"briser la glace\",\n            \"de\": \"das Eis brechen\",\n            \"am\": \"በረዶውን መስበር\"\n        },\n        \"hit the books\": {\n            \"es\": \"estudiar mucho\",\n            \"fr\": \"\\xe9tudier dur\",\n            \"de\": \"b\\xfcffeln\",\n            \"am\": \"መጽሐፍትን መምታት\"\n        },\n        \"piece of cake\": {\n            \"es\": \"pan comido\",\n            \"fr\": \"du g\\xe2teau\",\n            \"de\": \"ein Kinderspiel\",\n            \"am\": \"የኬክ ቁራጭ\"\n        },\n        \"spill the beans\": {\n            \"es\": \"revelar el secreto\",\n            \"fr\": \"vendre la m\\xe8che\",\n            \"de\": \"die Bohnen versch\\xfctten\",\n            \"am\": \"ባቄላውን መፍሰስ\"\n        },\n        \"cost an arm and a leg\": {\n            \"es\": \"costar un ojo de la cara\",\n            \"fr\": \"co\\xfbter les yeux de la t\\xeate\",\n            \"de\": \"ein Verm\\xf6gen kosten\",\n            \"am\": \"እጅና እግር ማስወጣት\"\n        },\n        \"bite the bullet\": {\n            \"es\": \"apretar los dientes\",\n            \"fr\": \"serrer les dents\",\n            \"de\": \"in den sauren Apfel bei\\xdfen\",\n            \"am\": \"ጥይቱን መንከስ\"\n        },\n        \"break a leg\": {\n            \"es\": \"que tengas suerte\",\n            \"fr\": \"bonne chance\",\n            \"de\": \"Hals- und Beinbruch\",\n            \"am\": \"እግርህን ስበር\"\n        },\n        \"call it a day\": {\n            \"es\": \"dar por terminado\",\n            \"fr\": \"s'arr\\xeater l\\xe0\",\n            \"de\": \"Feierabend machen\",\n            \"am\": \"ቀን ብለን መጥራት\"\n        },\n        \"better late than never\": {\n            \"es\": \"m\\xe1s vale tarde que nunca\",\n            \"fr\": \"mieux vaut tard que jamais\",\n            \"de\": \"besser sp\\xe4t als nie\",\n            \"am\": \"ከአልመጣም ዘግይቶ መምጣት ይሻላል\"\n        },\n        \"when pigs fly\": {\n            \"es\": \"cuando las ranas cr\\xeden pelo\",\n            \"fr\": \"quand les poules auront des dents\",\n            \"de\": \"wenn Schweine fliegen\",\n            \"am\": \"አሳማዎች በሚበሩበት ጊዜ\"\n        },\n        \"don't cry over spilled milk\": {\n            \"es\": \"no llores sobre la leche derramada\",\n            \"fr\": \"ne pleure pas sur le lait renvers\\xe9\",\n            \"de\": \"weine nicht \\xfcber versch\\xfcttete Milch\",\n            \"am\": \"በተፈሰሰ ወተት ላይ አትልቅ\"\n        },\n        \"cut to the chase\": {\n            \"es\": \"ir al grano\",\n            \"fr\": \"aller droit au but\",\n            \"de\": \"zur Sache kommen\",\n            \"am\": \"ወደ ዋናው ጉዳይ መሄድ\"\n        },\n        \"get the ball rolling\": {\n            \"es\": \"poner la pelota en movimiento\",\n            \"fr\": \"faire bouger les choses\",\n            \"de\": \"den Ball ins Rollen bringen\",\n            \"am\": \"ኳሱን ማንከባለል\"\n        },\n        \"hang in there\": {\n            \"es\": \"aguanta\",\n            \"fr\": \"tiens bon\",\n            \"de\": \"halte durch\",\n            \"am\": \"እዚያ ተንጠልጥል\"\n        },\n        \"it's raining cats and dogs\": {\n            \"es\": \"llueve a c\\xe1ntaros\",\n            \"fr\": \"il pleut des cordes\",\n            \"de\": \"es regnet Bindf\\xe4den\",\n            \"am\": \"ድመቶችና ውሾች እየዘነቡ ነው\"\n        },\n        \"kill two birds with one stone\": {\n            \"es\": \"matar dos p\\xe1jaros de un tiro\",\n            \"fr\": \"faire d'une pierre deux coups\",\n            \"de\": \"zwei Fliegen mit einer Klappe schlagen\",\n            \"am\": \"በአንድ ድንጋይ ሁለት ወፎችን መግደል\"\n        },\n        \"let the cat out of the bag\": {\n            \"es\": \"dejar salir el gato de la bolsa\",\n            \"fr\": \"vendre la m\\xe8che\",\n            \"de\": \"die Katze aus dem Sack lassen\",\n            \"am\": \"ድመቱን ከቦርሳው ውስጥ መልቀቅ\"\n        },\n        \"once in a blue moon\": {\n            \"es\": \"una vez cada muerte de obispo\",\n            \"fr\": \"tous les trente-six du mois\",\n            \"de\": \"alle Jubeljahre\",\n            \"am\": \"በሰማያዊ ጨረቃ ጊዜ\"\n        },\n        \"the ball is in your court\": {\n            \"es\": \"la pelota est\\xe1 en tu cancha\",\n            \"fr\": \"la balle est dans ton camp\",\n            \"de\": \"der Ball liegt bei dir\",\n            \"am\": \"ኳሱ በአንተ ሜዳ ላይ ነው\"\n        },\n        \"under the weather\": {\n            \"es\": \"indispuesto\",\n            \"fr\": \"pas dans son assiette\",\n            \"de\": \"nicht auf der H\\xf6he\",\n            \"am\": \"ከአየር ሁኔታ በታች\"\n        },\n        \"a blessing in disguise\": {\n            \"es\": \"no hay mal que por bien no venga\",\n            \"fr\": \"\\xe0 quelque chose malheur est bon\",\n            \"de\": \"ein Gl\\xfcck im Ungl\\xfcck\",\n            \"am\": \"በመሸፈኛ ውስጥ ያለ በረከት\"\n        },\n        \"every cloud has a silver lining\": {\n            \"es\": \"no hay mal que por bien no venga\",\n            \"fr\": \"apr\\xe8s la pluie, le beau temps\",\n            \"de\": \"nach Regen kommt Sonnenschein\",\n            \"am\": \"እያንዳንዱ ደመና የብር መስመር አለው\"\n        },\n        \"go the extra mile\": {\n            \"es\": \"hacer un esfuerzo adicional\",\n            \"fr\": \"faire un effort suppl\\xe9mentaire\",\n            \"de\": \"eine Extrameile gehen\",\n            \"am\": \"ተጨማሪ ማይል መሄድ\"\n        },\n        \"it takes two to tango\": {\n            \"es\": \"se necesitan dos para bailar tango\",\n            \"fr\": \"il faut \\xeatre deux pour danser\",\n            \"de\": \"zum Tango geh\\xf6ren zwei\",\n            \"am\": \"ታንጎ ለመጨፍር ሁለት ይወስዳል\"\n        },\n        \"keep your chin up\": {\n            \"es\": \"mant\\xe9n la cabeza alta\",\n            \"fr\": \"garde le moral\",\n            \"de\": \"lass den Kopf nicht h\\xe4ngen\",\n            \"am\": \"አገናህን ወደ ላይ አቆይ\"\n        },\n        \"make a long story short\": {\n            \"es\": \"para resumir\",\n            \"fr\": \"pour faire court\",\n            \"de\": \"um es kurz zu machen\",\n            \"am\": \"ረጅም ታሪክን አጭር ማድረግ\"\n        },\n        \"no pain no gain\": {\n            \"es\": \"sin dolor no hay ganancia\",\n            \"fr\": \"on n'a rien sans rien\",\n            \"de\": \"ohne Flei\\xdf kein Preis\",\n            \"am\": \"ህመም ከሌለ ትርፍ የለም\"\n        },\n        \"practice makes perfect\": {\n            \"es\": \"la pr\\xe1ctica hace al maestro\",\n            \"fr\": \"c'est en forgeant qu'on devient forgeron\",\n            \"de\": \"\\xdcbung macht den Meister\",\n            \"am\": \"ልምምድ ፍጹምነት ያመጣል\"\n        },\n        \"the early bird catches the worm\": {\n            \"es\": \"al que madruga, Dios le ayuda\",\n            \"fr\": \"l'avenir appartient \\xe0 ceux qui se l\\xe8vent t\\xf4t\",\n            \"de\": \"der fr\\xfche Vogel f\\xe4ngt den Wurm\",\n            \"am\": \"ቀደም ያለ ወፍ ትል ይይዛል\"\n        },\n        \"you can't judge a book by its cover\": {\n            \"es\": \"no juzgues un libro por su portada\",\n            \"fr\": \"l'habit ne fait pas le moine\",\n            \"de\": \"man soll ein Buch nicht nach seinem Einband beurteilen\",\n            \"am\": \"መጽሐፍን በሽፋኑ መፍረድ አይችልም\"\n        }\n    };\n    return translations[phrase.toLowerCase()] || {};\n}\n// Pre-defined definitions for common English idioms\nfunction getPhraseDefinition(phrase) {\n    const definitions = {\n        \"break the ice\": \"To initiate conversation or social interaction in a comfortable way\",\n        \"hit the books\": \"To study hard or intensively\",\n        \"piece of cake\": \"Something that is very easy to do\",\n        \"spill the beans\": \"To reveal a secret or disclose confidential information\",\n        \"cost an arm and a leg\": \"To be very expensive\",\n        \"bite the bullet\": \"To face a difficult situation with courage\",\n        \"break a leg\": \"Good luck! (used especially in theater)\",\n        \"call it a day\": \"To stop working or end an activity\",\n        \"better late than never\": \"It's better to do something late than not at all\",\n        \"when pigs fly\": \"Never; something that will never happen\",\n        \"don't cry over spilled milk\": \"Don't worry about past mistakes that cannot be changed\",\n        \"cut to the chase\": \"Get to the point; skip the unnecessary details\",\n        \"get the ball rolling\": \"Start something; begin an activity or process\",\n        \"hang in there\": \"Persevere; don't give up during difficult times\",\n        \"it's raining cats and dogs\": \"It's raining very heavily\",\n        \"kill two birds with one stone\": \"Accomplish two things with one action\",\n        \"let the cat out of the bag\": \"Reveal a secret accidentally\",\n        \"once in a blue moon\": \"Very rarely; almost never\",\n        \"the ball is in your court\": \"It's your turn to take action or make a decision\",\n        \"under the weather\": \"Feeling sick or unwell\",\n        \"a blessing in disguise\": \"Something that seems bad but turns out to be good\",\n        \"every cloud has a silver lining\": \"There's something positive in every negative situation\",\n        \"go the extra mile\": \"Make an additional effort; do more than expected\",\n        \"it takes two to tango\": \"Both parties are responsible for a situation\",\n        \"keep your chin up\": \"Stay positive; don't get discouraged\",\n        \"make a long story short\": \"To summarize; to get to the point quickly\",\n        \"no pain no gain\": \"You must work hard or suffer to achieve something worthwhile\",\n        \"practice makes perfect\": \"Doing something repeatedly will help you become better at it\",\n        \"the early bird catches the worm\": \"People who wake up early or act quickly will be successful\",\n        \"you can't judge a book by its cover\": \"Don't judge someone or something based on appearance alone\"\n    };\n    return definitions[phrase.toLowerCase()] || `An English idiom: \"${phrase}\"`;\n}\n// Pre-defined example sentences for common English idioms\nfunction getPhraseExamples(phrase) {\n    const examples = {\n        \"break the ice\": [\n            \"John told a funny joke to break the ice at the meeting.\",\n            \"She brought cookies to break the ice with her new neighbors.\"\n        ],\n        \"hit the books\": [\n            \"I need to hit the books tonight for tomorrow's exam.\",\n            \"After failing the test, Maria decided to hit the books harder.\"\n        ],\n        \"piece of cake\": [\n            \"The math homework was a piece of cake for Sarah.\",\n            \"Don't worry about the interview, it'll be a piece of cake!\"\n        ],\n        \"spill the beans\": [\n            \"Tom accidentally spilled the beans about the surprise party.\",\n            \"Please don't spill the beans about our secret project.\"\n        ],\n        \"cost an arm and a leg\": [\n            \"That new car costs an arm and a leg!\",\n            \"The designer dress cost an arm and a leg, but it was worth it.\"\n        ],\n        \"bite the bullet\": [\n            \"I hate going to the dentist, but I'll have to bite the bullet.\",\n            \"She bit the bullet and asked her boss for a raise.\"\n        ],\n        \"break a leg\": [\n            \"Break a leg in your performance tonight!\",\n            \"The director told all the actors to break a leg before the show.\"\n        ],\n        \"call it a day\": [\n            \"We've been working for 10 hours, let's call it a day.\",\n            \"After finishing the project, they decided to call it a day.\"\n        ],\n        \"better late than never\": [\n            \"He arrived an hour late, but better late than never.\",\n            \"I finally learned to drive at 30 - better late than never!\"\n        ],\n        \"when pigs fly\": [\n            \"He'll clean his room when pigs fly.\",\n            \"She said she'd apologize when pigs fly.\"\n        ],\n        \"don't cry over spilled milk\": [\n            \"You failed the test, but don't cry over spilled milk - study harder next time.\",\n            \"The project didn't work out, but don't cry over spilled milk.\"\n        ],\n        \"cut to the chase\": [\n            \"Let's cut to the chase - what do you really want?\",\n            \"I don't have much time, so let's cut to the chase.\"\n        ],\n        \"get the ball rolling\": [\n            \"Let's get the ball rolling on this project.\",\n            \"To get the ball rolling, I'll make the first presentation.\"\n        ],\n        \"hang in there\": [\n            \"I know work is tough right now, but hang in there.\",\n            \"Hang in there - things will get better soon.\"\n        ],\n        \"it's raining cats and dogs\": [\n            \"We can't go outside - it's raining cats and dogs!\",\n            \"The picnic was cancelled because it was raining cats and dogs.\"\n        ],\n        \"kill two birds with one stone\": [\n            \"I'll visit the bank and post office together to kill two birds with one stone.\",\n            \"By studying abroad, she killed two birds with one stone - learning the language and getting a degree.\"\n        ],\n        \"let the cat out of the bag\": [\n            \"Don't let the cat out of the bag about the surprise party!\",\n            \"He accidentally let the cat out of the bag about their engagement.\"\n        ],\n        \"once in a blue moon\": [\n            \"I only eat fast food once in a blue moon.\",\n            \"She visits her hometown once in a blue moon.\"\n        ],\n        \"the ball is in your court\": [\n            \"I've made my offer, now the ball is in your court.\",\n            \"We've given you all the information - the ball is in your court.\"\n        ],\n        \"under the weather\": [\n            \"I'm feeling a bit under the weather today.\",\n            \"She stayed home because she was under the weather.\"\n        ],\n        \"a blessing in disguise\": [\n            \"Losing that job was a blessing in disguise - I found a much better one.\",\n            \"The rain ruining our picnic was a blessing in disguise - we had a great time indoors.\"\n        ],\n        \"every cloud has a silver lining\": [\n            \"Don't worry about the setback - every cloud has a silver lining.\",\n            \"Even though he failed the exam, every cloud has a silver lining - now he knows what to study.\"\n        ],\n        \"go the extra mile\": [\n            \"She always goes the extra mile to help her customers.\",\n            \"If you want to succeed, you need to go the extra mile.\"\n        ],\n        \"it takes two to tango\": [\n            \"Don't blame just him for the argument - it takes two to tango.\",\n            \"Both companies are responsible for the failed merger - it takes two to tango.\"\n        ],\n        \"keep your chin up\": [\n            \"I know you're disappointed, but keep your chin up.\",\n            \"Keep your chin up - tomorrow is a new day.\"\n        ],\n        \"make a long story short\": [\n            \"To make a long story short, we missed our flight.\",\n            \"Make a long story short - did you get the job or not?\"\n        ],\n        \"no pain no gain\": [\n            \"Exercise is hard, but no pain no gain.\",\n            \"Learning a new language is difficult, but no pain no gain.\"\n        ],\n        \"practice makes perfect\": [\n            \"Keep playing piano every day - practice makes perfect.\",\n            \"Don't worry about making mistakes - practice makes perfect.\"\n        ],\n        \"the early bird catches the worm\": [\n            \"I always arrive at work early - the early bird catches the worm.\",\n            \"She got the best deals at the sale because the early bird catches the worm.\"\n        ],\n        \"you can't judge a book by its cover\": [\n            \"He looks tough, but he's actually very kind - you can't judge a book by its cover.\",\n            \"The restaurant doesn't look fancy, but the food is amazing - you can't judge a book by its cover.\"\n        ]\n    };\n    return examples[phrase.toLowerCase()] || [\n        `Here's an example of \"${phrase}\" in context.`\n    ];\n}\n// Function to translate example sentences with context\nasync function translateExampleSentence(englishSentence, phrase, targetLanguage) {\n    // Get the phrase translation\n    const phraseTranslations = getPhraseTranslations(phrase);\n    const translatedPhrase = phraseTranslations[targetLanguage] || phrase;\n    // Pre-defined full sentence translations for better accuracy\n    const sentenceTranslations = {\n        // get the ball rolling\n        \"Let's get the ball rolling on this project.\": {\n            \"es\": \"Pongamos en marcha este proyecto.\",\n            \"am\": \"ይህንን ፕሮጀክት እንጀምር።\",\n            \"fr\": \"Lan\\xe7ons ce projet.\",\n            \"de\": \"Lassen Sie uns dieses Projekt in Gang bringen.\"\n        },\n        \"To get the ball rolling, I'll make the first presentation.\": {\n            \"es\": \"Para empezar, har\\xe9 la primera presentaci\\xf3n.\",\n            \"am\": \"ለመጀመር፣ የመጀመሪያውን አቀራረብ አደርጋለሁ።\",\n            \"fr\": \"Pour commencer, je ferai la premi\\xe8re pr\\xe9sentation.\",\n            \"de\": \"Um anzufangen, werde ich die erste Pr\\xe4sentation machen.\"\n        },\n        // break the ice\n        \"John told a funny joke to break the ice at the meeting.\": {\n            \"es\": \"John cont\\xf3 un chiste divertido para romper el hielo en la reuni\\xf3n.\",\n            \"am\": \"ጆን በስብሰባው ላይ በረዶውን ለመስበር አስቂኝ ቀልድ ተናገረ።\",\n            \"fr\": \"John a racont\\xe9 une blague amusante pour briser la glace lors de la r\\xe9union.\",\n            \"de\": \"John erz\\xe4hlte einen lustigen Witz, um das Eis bei der Besprechung zu brechen.\"\n        },\n        \"She brought cookies to break the ice with her new neighbors.\": {\n            \"es\": \"Trajo galletas para romper el hielo con sus nuevos vecinos.\",\n            \"am\": \"ከአዳዲስ ጎረቤቶቿ ጋር በረዶውን ለመስበር ኩኪዎችን አመጣች።\",\n            \"fr\": \"Elle a apport\\xe9 des biscuits pour briser la glace avec ses nouveaux voisins.\",\n            \"de\": \"Sie brachte Kekse mit, um das Eis mit ihren neuen Nachbarn zu brechen.\"\n        },\n        // hit the books\n        \"I need to hit the books tonight for tomorrow's exam.\": {\n            \"es\": \"Necesito estudiar mucho esta noche para el examen de ma\\xf1ana.\",\n            \"am\": \"ለነገ ፈተና ዛሬ ማታ መጽሐፍትን መምታት አለብኝ።\",\n            \"fr\": \"Je dois \\xe9tudier dur ce soir pour l'examen de demain.\",\n            \"de\": \"Ich muss heute Abend f\\xfcr die morgige Pr\\xfcfung b\\xfcffeln.\"\n        },\n        \"After failing the test, Maria decided to hit the books harder.\": {\n            \"es\": \"Despu\\xe9s de reprobar el examen, Mar\\xeda decidi\\xf3 estudiar m\\xe1s duro.\",\n            \"am\": \"ፈተናውን ካልፈለገች በኋላ ማሪያ መጽሐፍቶቹን በጠንካራ መምታት ወሰነች።\",\n            \"fr\": \"Apr\\xe8s avoir \\xe9chou\\xe9 au test, Maria a d\\xe9cid\\xe9 d'\\xe9tudier plus dur.\",\n            \"de\": \"Nachdem sie die Pr\\xfcfung nicht bestanden hatte, beschloss Maria, h\\xe4rter zu lernen.\"\n        },\n        // piece of cake\n        \"The math homework was a piece of cake for Sarah.\": {\n            \"es\": \"La tarea de matem\\xe1ticas fue pan comido para Sarah.\",\n            \"am\": \"የሂሳብ ስራው ለሳራ የኬክ ቁራጭ ነበር።\",\n            \"fr\": \"Les devoirs de maths \\xe9taient du g\\xe2teau pour Sarah.\",\n            \"de\": \"Die Mathe-Hausaufgaben waren ein Kinderspiel f\\xfcr Sarah.\"\n        },\n        \"Don't worry about the interview, it'll be a piece of cake!\": {\n            \"es\": \"No te preocupes por la entrevista, \\xa1ser\\xe1 pan comido!\",\n            \"am\": \"ስለ ቃለ መጠይቁ አትጨነቅ፣ የኬክ ቁራጭ ይሆናል!\",\n            \"fr\": \"Ne t'inqui\\xe8te pas pour l'entretien, ce sera du g\\xe2teau !\",\n            \"de\": \"Mach dir keine Sorgen wegen des Interviews, es wird ein Kinderspiel!\"\n        },\n        // the ball is in your court\n        \"I've made my offer, now the ball is in your court.\": {\n            \"es\": \"He hecho mi oferta, ahora la pelota est\\xe1 en tu cancha.\",\n            \"am\": \"ቀረቡን አቅርቤያለሁ፣ አሁን ኳሱ በአንተ ሜዳ ላይ ነው።\",\n            \"fr\": \"J'ai fait mon offre, maintenant la balle est dans ton camp.\",\n            \"de\": \"Ich habe mein Angebot gemacht, jetzt liegt der Ball bei dir.\"\n        },\n        \"We've given you all the information - the ball is in your court.\": {\n            \"es\": \"Te hemos dado toda la informaci\\xf3n - la pelota est\\xe1 en tu cancha.\",\n            \"am\": \"ሁሉንም መረጃ ሰጥተናችኋል - ኳሱ በአንተ ሜዳ ላይ ነው።\",\n            \"fr\": \"Nous vous avons donn\\xe9 toutes les informations - la balle est dans votre camp.\",\n            \"de\": \"Wir haben Ihnen alle Informationen gegeben - der Ball liegt bei Ihnen.\"\n        },\n        // hang in there\n        \"I know work is tough right now, but hang in there.\": {\n            \"es\": \"S\\xe9 que el trabajo est\\xe1 dif\\xedcil ahora, pero aguanta.\",\n            \"am\": \"ስራው አሁን ከባድ እንደሆነ አውቃለሁ፣ ግን እዚያ ተንጠልጥል።\",\n            \"fr\": \"Je sais que le travail est dur en ce moment, mais tiens bon.\",\n            \"de\": \"Ich wei\\xdf, die Arbeit ist gerade schwer, aber halte durch.\"\n        },\n        \"Hang in there - things will get better soon.\": {\n            \"es\": \"Aguanta - las cosas mejorar\\xe1n pronto.\",\n            \"am\": \"እዚያ ተንጠልጥል - ነገሮች በቅርቡ ይሻላሉ።\",\n            \"fr\": \"Tiens bon - les choses vont bient\\xf4t s'am\\xe9liorer.\",\n            \"de\": \"Halte durch - die Dinge werden bald besser.\"\n        },\n        // cut to the chase\n        \"Let's cut to the chase - what do you really want?\": {\n            \"es\": \"Vamos al grano - \\xbfqu\\xe9 quieres realmente?\",\n            \"am\": \"ወደ ዋናው ጉዳይ እንሂድ - በእውነት ምን ትፈልጋለህ?\",\n            \"fr\": \"Allons droit au but - que veux-tu vraiment ?\",\n            \"de\": \"Kommen wir zur Sache - was willst du wirklich?\"\n        },\n        \"I don't have much time, so let's cut to the chase.\": {\n            \"es\": \"No tengo mucho tiempo, as\\xed que vamos al grano.\",\n            \"am\": \"ብዙ ጊዜ የለኝም፣ ስለዚህ ወደ ዋናው ጉዳይ እንሂድ።\",\n            \"fr\": \"Je n'ai pas beaucoup de temps, alors allons droit au but.\",\n            \"de\": \"Ich habe nicht viel Zeit, also kommen wir zur Sache.\"\n        },\n        // go the extra mile\n        \"She always goes the extra mile to help her customers.\": {\n            \"es\": \"Ella siempre hace un esfuerzo adicional para ayudar a sus clientes.\",\n            \"am\": \"እሷ ሁልጊዜ ደንበኞቿን ለመርዳት ተጨማሪ ማይል ትሄዳለች።\",\n            \"fr\": \"Elle fait toujours un effort suppl\\xe9mentaire pour aider ses clients.\",\n            \"de\": \"Sie geht immer eine Extrameile, um ihren Kunden zu helfen.\"\n        },\n        \"If you want to succeed, you need to go the extra mile.\": {\n            \"es\": \"Si quieres tener \\xe9xito, necesitas hacer un esfuerzo adicional.\",\n            \"am\": \"ለማሳካት ከፈለግክ ተጨማሪ ማይል መሄድ አለብህ።\",\n            \"fr\": \"Si tu veux r\\xe9ussir, tu dois faire un effort suppl\\xe9mentaire.\",\n            \"de\": \"Wenn du erfolgreich sein willst, musst du eine Extrameile gehen.\"\n        }\n    };\n    // Check if we have a pre-defined translation for this exact sentence\n    if (sentenceTranslations[englishSentence] && sentenceTranslations[englishSentence][targetLanguage]) {\n        return sentenceTranslations[englishSentence][targetLanguage];\n    }\n    // Try Google Translate for full sentence translation\n    try {\n        console.log(`Attempting Google Translate for: \"${englishSentence}\"`);\n        const googleTranslation = await (0,_lib_wordnik__WEBPACK_IMPORTED_MODULE_3__.translateText)(englishSentence, targetLanguage);\n        // If Google Translate returned something different from the original, use it\n        if (googleTranslation !== englishSentence) {\n            console.log(`Google Translate success: \"${googleTranslation}\"`);\n            return googleTranslation;\n        }\n    } catch (error) {\n        console.error(\"Google Translate failed, using fallback:\", error);\n    }\n    // Enhanced fallback translation with comprehensive patterns\n    const enhancedTranslations = {\n        \"am\": {\n            // Complete sentence patterns for common structures\n            \"Don't blame just him for the argument\": \"ለክርክሩ እሱን ብቻ አትወቅስ\",\n            \"Both companies are responsible for the failed merger\": \"ሁለቱም ኩባንያዎች ለያልተሳካው ውህደት ተጠያቂ ናቸው\",\n            \"We can't go outside\": \"ወደ ውጭ መውጣት አንችልም\",\n            \"The picnic was cancelled because\": \"ፒክኒኩ ተሰርዟል ምክንያቱም\",\n            \"Break a leg in your performance tonight\": \"ዛሬ ማታ በአፈጻጸምህ ላይ እግርህን ስበር\",\n            \"The director told all the actors to\": \"ዳይሬክተሩ ለሁሉም ተዋናዮች ተናግሯል\",\n            \"You failed the test, but\": \"ፈተናውን ወድቀሃል፣ ግን\",\n            \"study harder next time\": \"በሚቀጥለው ጊዜ በጠንካራ ተማር\",\n            \"The project didn't work out\": \"ፕሮጀክቱ አልሰራም\",\n            \"That new car costs\": \"ያ አዲስ መኪና ዋጋው\",\n            \"The designer dress cost\": \"የዲዛይነር ቀሚሱ ዋጋ\",\n            \"but it was worth it\": \"ግን ዋጋው ነበረው\",\n            // Word-level patterns\n            \"Don't blame\": \"አትወቅስ\",\n            \"just him\": \"እሱን ብቻ\",\n            \"for the\": \"ለ\",\n            \"argument\": \"ክርክር\",\n            \"Both companies\": \"ሁለቱም ኩባንያዎች\",\n            \"are responsible\": \"ተጠያቂ ናቸው\",\n            \"failed merger\": \"ያልተሳካ ውህደት\",\n            \"We can't\": \"አንችልም\",\n            \"go outside\": \"ወደ ውጭ መሄድ\",\n            \"Break a leg\": \"እግርህን ስበር\",\n            \"performance\": \"አፈጻጸም\",\n            \"tonight\": \"ዛሬ ማታ\",\n            \"director\": \"ዳይሬክተር\",\n            \"actors\": \"ተዋናዮች\",\n            \"You failed\": \"ወድቀሃል\",\n            \"the test\": \"ፈተናውን\",\n            \"study harder\": \"በጠንካራ ተማር\",\n            \"next time\": \"በሚቀጥለው ጊዜ\",\n            \"project\": \"ፕሮጀክት\",\n            \"didn't work out\": \"አልሰራም\",\n            \"new car\": \"አዲስ መኪና\",\n            \"costs\": \"ዋጋው\",\n            \"designer dress\": \"የዲዛይነር ቀሚስ\",\n            \"worth it\": \"ዋጋው ነበረው\",\n            \"Let's\": \"እንሂድ\",\n            \"I'll\": \"እኔ እሰራለሁ\",\n            \"To\": \"ለ\",\n            \"I need to\": \"እኔ መሰራት አለብኝ\",\n            \"Don't worry about\": \"አትጨነቅ ስለ\",\n            \"it'll be\": \"ይሆናል\",\n            \"I've made\": \"አድርጌያለሁ\",\n            \"now\": \"አሁን\",\n            \"We've given you\": \"ሰጥተናችኋል\"\n        },\n        \"es\": {\n            // Complete sentence patterns for common structures\n            \"Don't blame just him for the argument\": \"No culpes solo a \\xe9l por la discusi\\xf3n\",\n            \"Both companies are responsible for the failed merger\": \"Ambas empresas son responsables de la fusi\\xf3n fallida\",\n            \"We can't go outside\": \"No podemos salir\",\n            \"The picnic was cancelled because\": \"El picnic fue cancelado porque\",\n            \"Break a leg in your performance tonight\": \"Que tengas suerte en tu actuaci\\xf3n esta noche\",\n            \"The director told all the actors to\": \"El director les dijo a todos los actores que\",\n            \"You failed the test, but\": \"Reprobaste el examen, pero\",\n            \"study harder next time\": \"estudia m\\xe1s duro la pr\\xf3xima vez\",\n            \"The project didn't work out\": \"El proyecto no funcion\\xf3\",\n            \"That new car costs\": \"Ese auto nuevo cuesta\",\n            \"The designer dress cost\": \"El vestido de dise\\xf1ador cost\\xf3\",\n            \"but it was worth it\": \"pero vali\\xf3 la pena\",\n            // Word-level patterns\n            \"Don't blame\": \"No culpes\",\n            \"just him\": \"solo a \\xe9l\",\n            \"for the\": \"por la\",\n            \"argument\": \"discusi\\xf3n\",\n            \"Both companies\": \"Ambas empresas\",\n            \"are responsible\": \"son responsables\",\n            \"failed merger\": \"fusi\\xf3n fallida\",\n            \"We can't\": \"No podemos\",\n            \"go outside\": \"salir\",\n            \"Break a leg\": \"Que tengas suerte\",\n            \"performance\": \"actuaci\\xf3n\",\n            \"tonight\": \"esta noche\",\n            \"director\": \"director\",\n            \"actors\": \"actores\",\n            \"You failed\": \"Reprobaste\",\n            \"the test\": \"el examen\",\n            \"study harder\": \"estudia m\\xe1s duro\",\n            \"next time\": \"la pr\\xf3xima vez\",\n            \"project\": \"proyecto\",\n            \"didn't work out\": \"no funcion\\xf3\",\n            \"new car\": \"auto nuevo\",\n            \"costs\": \"cuesta\",\n            \"designer dress\": \"vestido de dise\\xf1ador\",\n            \"worth it\": \"vali\\xf3 la pena\",\n            \"Let's\": \"Vamos a\",\n            \"I'll\": \"Voy a\",\n            \"To\": \"Para\",\n            \"I need to\": \"Necesito\",\n            \"Don't worry about\": \"No te preocupes por\",\n            \"it'll be\": \"ser\\xe1\",\n            \"I've made\": \"He hecho\",\n            \"now\": \"ahora\",\n            \"We've given you\": \"Te hemos dado\"\n        }\n    };\n    // Try enhanced sentence-level translation first\n    if (enhancedTranslations[targetLanguage]) {\n        // Check for complete sentence matches first\n        for (const [englishPattern, translation] of Object.entries(enhancedTranslations[targetLanguage])){\n            if (englishSentence.toLowerCase().includes(englishPattern.toLowerCase())) {\n                // If we find a sentence pattern, use it as base and replace the phrase\n                let result = englishSentence.replace(new RegExp(englishPattern, \"gi\"), translation);\n                result = result.replace(new RegExp(phrase, \"gi\"), translatedPhrase);\n                return result;\n            }\n        }\n        // If no sentence pattern matches, do word-by-word replacement\n        let result = englishSentence.replace(new RegExp(phrase, \"gi\"), translatedPhrase);\n        // Apply word-level patterns\n        Object.entries(enhancedTranslations[targetLanguage]).forEach(([english, translated])=>{\n            result = result.replace(new RegExp(english, \"gi\"), translated);\n        });\n        return result;\n    }\n    // Final fallback: just replace the phrase\n    return englishSentence.replace(new RegExp(phrase, \"gi\"), translatedPhrase);\n}\n// Function to get similar phrases/synonyms\nfunction getSimilarPhrases(phrase) {\n    const similarPhrases = {\n        \"break the ice\": [\n            \"start a conversation\",\n            \"make introductions\",\n            \"ease tension\"\n        ],\n        \"hit the books\": [\n            \"study hard\",\n            \"crack the books\",\n            \"burn the midnight oil\"\n        ],\n        \"piece of cake\": [\n            \"easy as pie\",\n            \"a walk in the park\",\n            \"child's play\"\n        ],\n        \"spill the beans\": [\n            \"let the cat out of the bag\",\n            \"reveal the secret\",\n            \"tell all\"\n        ],\n        \"cost an arm and a leg\": [\n            \"very expensive\",\n            \"break the bank\",\n            \"cost a fortune\"\n        ],\n        \"bite the bullet\": [\n            \"face the music\",\n            \"take the plunge\",\n            \"grin and bear it\"\n        ],\n        \"break a leg\": [\n            \"good luck\",\n            \"knock 'em dead\",\n            \"best wishes\"\n        ],\n        \"call it a day\": [\n            \"wrap it up\",\n            \"finish up\",\n            \"call it quits\"\n        ],\n        \"cut to the chase\": [\n            \"get to the point\",\n            \"cut to the quick\",\n            \"bottom line\"\n        ],\n        \"get the ball rolling\": [\n            \"start the ball rolling\",\n            \"kick things off\",\n            \"get started\"\n        ],\n        \"hang in there\": [\n            \"stick with it\",\n            \"don't give up\",\n            \"persevere\"\n        ],\n        \"it's raining cats and dogs\": [\n            \"pouring rain\",\n            \"heavy downpour\",\n            \"bucketing down\"\n        ],\n        \"kill two birds with one stone\": [\n            \"hit two targets\",\n            \"double up\",\n            \"multitask\"\n        ],\n        \"let the cat out of the bag\": [\n            \"spill the beans\",\n            \"reveal the secret\",\n            \"blow the whistle\"\n        ],\n        \"once in a blue moon\": [\n            \"rarely\",\n            \"seldom\",\n            \"hardly ever\"\n        ],\n        \"the ball is in your court\": [\n            \"your turn\",\n            \"your move\",\n            \"up to you\"\n        ],\n        \"under the weather\": [\n            \"feeling sick\",\n            \"not well\",\n            \"out of sorts\"\n        ],\n        \"better late than never\": [\n            \"better than not at all\",\n            \"at least you made it\",\n            \"finally\"\n        ],\n        \"when pigs fly\": [\n            \"never\",\n            \"not in a million years\",\n            \"over my dead body\"\n        ],\n        \"don't cry over spilled milk\": [\n            \"what's done is done\",\n            \"let bygones be bygones\",\n            \"move on\"\n        ],\n        \"a blessing in disguise\": [\n            \"silver lining\",\n            \"hidden benefit\",\n            \"good fortune\"\n        ],\n        \"every cloud has a silver lining\": [\n            \"look on the bright side\",\n            \"there's hope\",\n            \"positive outlook\"\n        ],\n        \"go the extra mile\": [\n            \"above and beyond\",\n            \"give 110%\",\n            \"exceed expectations\"\n        ],\n        \"it takes two to tango\": [\n            \"both sides responsible\",\n            \"mutual responsibility\",\n            \"shared blame\"\n        ],\n        \"keep your chin up\": [\n            \"stay positive\",\n            \"don't lose hope\",\n            \"remain optimistic\"\n        ],\n        \"make a long story short\": [\n            \"in summary\",\n            \"to cut to the chase\",\n            \"bottom line\"\n        ],\n        \"no pain no gain\": [\n            \"work hard for success\",\n            \"sacrifice for achievement\",\n            \"earn your rewards\"\n        ],\n        \"practice makes perfect\": [\n            \"repetition improves skill\",\n            \"keep trying\",\n            \"perfect through practice\"\n        ],\n        \"the early bird catches the worm\": [\n            \"first come first served\",\n            \"early advantage\",\n            \"punctuality pays\"\n        ],\n        \"you can't judge a book by its cover\": [\n            \"don't judge by appearance\",\n            \"looks deceive\",\n            \"inner beauty matters\"\n        ]\n    };\n    return similarPhrases[phrase.toLowerCase()] || [\n        \"Similar expressions\",\n        \"Related phrases\",\n        \"Comparable idioms\"\n    ];\n}\n// Sample phrases for demo mode (since database is disabled)\nconst samplePhrases = [\n    {\n        id: \"1\",\n        englishPhrase: \"Break the ice\",\n        phraseType: \"idiom\",\n        translations: {\n            am: \"በረዶውን መስበር\",\n            es: \"Romper el hielo\",\n            zh: \"打破僵局\",\n            fr: \"Briser la glace\",\n            de: \"Das Eis brechen\",\n            ja: \"緊張をほぐす\",\n            ko: \"어색함을 깨다\",\n            ru: \"Растопить лед\",\n            ar: \"كسر الجليد\",\n            vi: \"Ph\\xe1 vỡ sự ngượng ng\\xf9ng\"\n        },\n        exampleSentences: [\n            {\n                english: \"He told a joke to break the ice at the meeting.\",\n                translated: {\n                    am: \"በስብሰባው ላይ በረዶውን ለመስበር ቀልድ ተናገረ።\",\n                    es: \"Cont\\xf3 un chiste para romper el hielo en la reuni\\xf3n.\",\n                    zh: \"他讲了个笑话来打破会议上的僵局。\",\n                    fr: \"Il a racont\\xe9 une blague pour briser la glace lors de la r\\xe9union.\",\n                    de: \"Er erz\\xe4hlte einen Witz, um das Eis bei der Besprechung zu brechen.\",\n                    ja: \"彼は会議で緊張をほぐすためにジョークを言った。\",\n                    ko: \"그는 회의에서 어색함을 깨기 위해 농담을 했다.\",\n                    ru: \"Он рассказал шутку, чтобы растопить лед на встрече.\",\n                    ar: \"حكى نكتة لكسر الجليد في الاجتماع.\",\n                    vi: \"Anh ấy kể một c\\xe2u chuyện cười để ph\\xe1 vỡ sự ngượng ng\\xf9ng trong cuộc họp.\"\n                }\n            }\n        ],\n        synonyms: [\n            \"Start a conversation\",\n            \"Make people feel comfortable\",\n            \"Ease tension\"\n        ]\n    },\n    {\n        id: \"2\",\n        englishPhrase: \"Hit the books\",\n        phraseType: \"idiom\",\n        translations: {\n            am: \"መጽሐፍትን መምታት\",\n            es: \"Ponerse a estudiar\",\n            zh: \"开始学习\",\n            fr: \"Se mettre aux \\xe9tudes\",\n            de: \"B\\xfccher w\\xe4lzen\",\n            ja: \"勉強する\",\n            ko: \"공부하다\",\n            ru: \"Засесть за книги\",\n            ar: \"البدء في الدراسة\",\n            vi: \"Bắt đầu học b\\xe0i\"\n        },\n        exampleSentences: [\n            {\n                english: \"I need to hit the books if I want to pass the exam.\",\n                translated: {\n                    am: \"ፈተናውን ማለፍ ከፈለግሁ መጽሐፍትን መምታት አለብኝ።\",\n                    es: \"Necesito ponerme a estudiar si quiero aprobar el examen.\",\n                    zh: \"如果我想通过考试，我需要开始学习。\",\n                    fr: \"Je dois me mettre aux \\xe9tudes si je veux r\\xe9ussir l'examen.\",\n                    de: \"Ich muss die B\\xfccher w\\xe4lzen, wenn ich die Pr\\xfcfung bestehen will.\",\n                    ja: \"試験に合格したいなら勉強しなければならない。\",\n                    ko: \"시험에 합격하려면 공부해야 한다.\",\n                    ru: \"Мне нужно засесть за книги, если я хочу сдать экзамен.\",\n                    ar: \"أحتاج للبدء في الدراسة إذا كنت أريد النجاح في الامتحان.\",\n                    vi: \"T\\xf4i cần bắt đầu học b\\xe0i nếu muốn đậu kỳ thi.\"\n                }\n            }\n        ],\n        synonyms: [\n            \"Study hard\",\n            \"Focus on learning\",\n            \"Prepare for exams\"\n        ]\n    },\n    {\n        id: \"3\",\n        englishPhrase: \"Piece of cake\",\n        phraseType: \"idiom\",\n        translations: {\n            am: \"የዳቦ ቁራሽ\",\n            es: \"Pan comido\",\n            zh: \"小菜一碟\",\n            fr: \"Du g\\xe2teau\",\n            de: \"Ein Kinderspiel\",\n            ja: \"朝飯前\",\n            ko: \"식은 죽 먹기\",\n            ru: \"Проще простого\",\n            ar: \"أمر سهل جداً\",\n            vi: \"Dễ như ăn b\\xe1nh\"\n        },\n        exampleSentences: [\n            {\n                english: \"Don't worry about the test, it's a piece of cake!\",\n                translated: {\n                    am: \"ስለ ፈተናው አትጨነቅ፣ የዳቦ ቁራሽ ነው!\",\n                    es: \"No te preocupes por el examen, \\xa1es pan comido!\",\n                    zh: \"别担心考试，这是小菜一碟！\",\n                    fr: \"Ne t'inqui\\xe8te pas pour le test, c'est du g\\xe2teau !\",\n                    de: \"Mach dir keine Sorgen wegen der Pr\\xfcfung, das ist ein Kinderspiel!\",\n                    ja: \"テストのことは心配しないで、朝飯前だよ！\",\n                    ko: \"시험 걱정하지 마, 식은 죽 먹기야!\",\n                    ru: \"Не волнуйся насчет теста, это проще простого!\",\n                    ar: \"لا تقلق بشأن الاختبار، إنه أمر سهل جداً!\",\n                    vi: \"Đừng lo lắng về b\\xe0i kiểm tra, n\\xf3 dễ như ăn b\\xe1nh!\"\n                }\n            }\n        ],\n        synonyms: [\n            \"Very easy\",\n            \"Simple task\",\n            \"No problem\"\n        ]\n    },\n    {\n        id: \"4\",\n        englishPhrase: \"Spill the beans\",\n        phraseType: \"idiom\",\n        translations: {\n            am: \"ሚስጢሩን መናገር\",\n            es: \"Soltar la sopa\",\n            zh: \"泄露秘密\",\n            fr: \"Vendre la m\\xe8che\",\n            de: \"Die Katze aus dem Sack lassen\",\n            ja: \"秘密をばらす\",\n            ko: \"비밀을 털어놓다\",\n            ru: \"Выдать секрет\",\n            ar: \"كشف السر\",\n            vi: \"Tiết lộ b\\xed mật\"\n        },\n        exampleSentences: [\n            {\n                english: \"Come on, spill the beans! What happened at the party?\",\n                translated: {\n                    am: \"ና፣ ሚስጢሩን ንገረኝ! በድግሱ ላይ ምን ሆነ?\",\n                    es: \"\\xa1Vamos, suelta la sopa! \\xbfQu\\xe9 pas\\xf3 en la fiesta?\",\n                    zh: \"快说吧，泄露秘密！聚会上发生了什么？\",\n                    fr: \"Allez, vends la m\\xe8che ! Que s'est-il pass\\xe9 \\xe0 la f\\xeate ?\",\n                    de: \"Komm schon, lass die Katze aus dem Sack! Was ist auf der Party passiert?\",\n                    ja: \"さあ、秘密をばらして！パーティーで何があったの？\",\n                    ko: \"자, 비밀을 털어놔! 파티에서 무슨 일이 있었어?\",\n                    ru: \"Давай, выдай секрет! Что случилось на вечеринке?\",\n                    ar: \"هيا، اكشف السر! ماذا حدث في الحفلة؟\",\n                    vi: \"N\\xe0o, tiết lộ b\\xed mật đi! Chuyện g\\xec đ\\xe3 xảy ra ở bữa tiệc?\"\n                }\n            }\n        ],\n        synonyms: [\n            \"Reveal a secret\",\n            \"Tell the truth\",\n            \"Confess\"\n        ]\n    },\n    {\n        id: \"5\",\n        englishPhrase: \"Cost an arm and a leg\",\n        phraseType: \"idiom\",\n        translations: {\n            am: \"በጣም ውድ መሆን\",\n            es: \"Costar un ojo de la cara\",\n            zh: \"非常昂贵\",\n            fr: \"Co\\xfbter les yeux de la t\\xeate\",\n            de: \"Ein Verm\\xf6gen kosten\",\n            ja: \"とても高い\",\n            ko: \"매우 비싸다\",\n            ru: \"Стоить целое состояние\",\n            ar: \"يكلف ثروة\",\n            vi: \"Rất đắt đỏ\"\n        },\n        exampleSentences: [\n            {\n                english: \"That new car costs an arm and a leg!\",\n                translated: {\n                    am: \"ያ አዲስ መኪና በጣም ውድ ነው!\",\n                    es: \"\\xa1Ese coche nuevo cuesta un ojo de la cara!\",\n                    zh: \"那辆新车非常昂贵！\",\n                    fr: \"Cette nouvelle voiture co\\xfbte les yeux de la t\\xeate !\",\n                    de: \"Das neue Auto kostet ein Verm\\xf6gen!\",\n                    ja: \"その新しい車はとても高い！\",\n                    ko: \"그 새 차는 매우 비싸다!\",\n                    ru: \"Эта новая машина стоит целое состояние!\",\n                    ar: \"تلك السيارة الجديدة تكلف ثروة!\",\n                    vi: \"Chiếc xe mới đ\\xf3 rất đắt đỏ!\"\n                }\n            }\n        ],\n        synonyms: [\n            \"Very expensive\",\n            \"Overpriced\",\n            \"Costly\"\n        ]\n    }\n];\nasync function GET(request) {\n    try {\n        const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_1__.getServerSession)(_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_2__.authOptions);\n        if (!session?.user) {\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n                error: \"Unauthorized\"\n            }, {\n                status: 401\n            });\n        }\n        const { searchParams } = new URL(request.url);\n        const preferredLanguage = session.user.preferredLanguage || \"es\";\n        const userId = session.user.id;\n        console.log(`User ${userId} preferred language: ${preferredLanguage}`);\n        // Get list of known phrases for this user to avoid repetition\n        let knownPhraseIds = [];\n        try {\n            const prisma = new _prisma_client__WEBPACK_IMPORTED_MODULE_4__.PrismaClient();\n            const knownPhrases = await prisma.userPhrase.findMany({\n                where: {\n                    userId: userId,\n                    status: \"known\"\n                },\n                select: {\n                    phraseId: true\n                }\n            });\n            knownPhraseIds = knownPhrases.map((p)=>p.phraseId);\n            console.log(`User has ${knownPhraseIds.length} known phrases`);\n            await prisma.$disconnect();\n        } catch (error) {\n            console.error(\"Error fetching known phrases:\", error);\n        }\n        // 70% chance to get a dynamic phrase from Wordnik, 30% chance to get a static phrase\n        const useDynamicPhrase = Math.random() < 0.7;\n        if (useDynamicPhrase) {\n            try {\n                console.log(\"Attempting to fetch dynamic phrase from Wordnik...\");\n                // Fetch a random phrase from Wordnik\n                const wordnikPhrase = await (0,_lib_wordnik__WEBPACK_IMPORTED_MODULE_3__.getRandomPhrase)();\n                if (wordnikPhrase && wordnikPhrase.phrase) {\n                    console.log(`Got phrase from Wordnik: \"${wordnikPhrase.phrase}\"`);\n                    // Create a definition from Wordnik data or use pre-defined ones\n                    const definition = wordnikPhrase.definitions.length > 0 ? wordnikPhrase.definitions[0].text : getPhraseDefinition(wordnikPhrase.phrase);\n                    // Create example sentences from Wordnik data\n                    const examples = wordnikPhrase.examples.slice(0, 2);\n                    // If no examples from Wordnik, create meaningful ones\n                    if (examples.length === 0) {\n                        const exampleSentences = getPhraseExamples(wordnikPhrase.phrase);\n                        examples.push(...exampleSentences.map((text)=>({\n                                text,\n                                title: \"Generated\"\n                            })));\n                    }\n                    // Use pre-defined translations for common phrases (since Google Translate has issues)\n                    const phraseTranslations = getPhraseTranslations(wordnikPhrase.phrase);\n                    const translatedPhrase = phraseTranslations[preferredLanguage] || wordnikPhrase.phrase;\n                    console.log(`Phrase: \"${wordnikPhrase.phrase}\" -> \"${translatedPhrase}\" (${preferredLanguage})`);\n                    const translatedExamples = await Promise.all(examples.map(async (example)=>({\n                            english: example.text,\n                            translated: {\n                                [preferredLanguage]: await translateExampleSentence(example.text, wordnikPhrase.phrase, preferredLanguage)\n                            }\n                        })));\n                    // Create consistent ID for the phrase\n                    const phraseId = `wordnik_${wordnikPhrase.phrase.replace(/\\s+/g, \"_\").toLowerCase().replace(/[^a-z0-9_]/g, \"\")}`;\n                    // Skip if user already knows this phrase\n                    if (knownPhraseIds.includes(phraseId)) {\n                        console.log(`Skipping known phrase: ${wordnikPhrase.phrase}`);\n                        // Try to get another phrase (recursive call with limit to prevent infinite loop)\n                        const retryCount = parseInt(searchParams.get(\"retryCount\") || \"0\");\n                        if (retryCount < 3) {\n                            const retryUrl = new URL(request.url);\n                            retryUrl.searchParams.set(\"retryCount\", (retryCount + 1).toString());\n                            return GET(new Request(retryUrl.toString()));\n                        }\n                    }\n                    // Format the response to match our Phrase interface\n                    const formattedPhrase = {\n                        id: phraseId,\n                        englishPhrase: wordnikPhrase.phrase,\n                        phraseType: \"idiom\",\n                        translations: {\n                            [preferredLanguage]: translatedPhrase\n                        },\n                        exampleSentences: translatedExamples,\n                        synonyms: getSimilarPhrases(wordnikPhrase.phrase),\n                        source: \"Wordnik + Google Translate\"\n                    };\n                    console.log(\"Successfully created dynamic phrase\");\n                    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(formattedPhrase);\n                }\n            } catch (error) {\n                console.log(\"Dynamic phrase fetch failed, falling back to static phrases:\", error);\n            }\n        }\n        // Fallback to static phrases (or if dynamic fetch failed)\n        console.log(\"Using static phrase\");\n        // Filter out known phrases from static phrases\n        const availablePhrases = samplePhrases.filter((phrase)=>!knownPhraseIds.includes(phrase.id));\n        if (availablePhrases.length === 0) {\n            // If user knows all phrases, reset and show a random one anyway\n            console.log(\"User knows all available phrases, showing random phrase\");\n            const randomIndex = Math.floor(Math.random() * samplePhrases.length);\n            return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(samplePhrases[randomIndex]);\n        }\n        const randomIndex = Math.floor(Math.random() * availablePhrases.length);\n        const randomPhrase = availablePhrases[randomIndex];\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json(randomPhrase);\n    } catch (error) {\n        console.error(\"Error fetching random phrase:\", error);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.json({\n            error: \"Failed to fetch phrase\"\n        }, {\n            status: 500\n        });\n    }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/api/phrases/random/route.ts\n");

/***/ }),

/***/ "(rsc)/./lib/wordnik.ts":
/*!************************!*\
  !*** ./lib/wordnik.ts ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getRandomPhrase: () => (/* binding */ getRandomPhrase),\n/* harmony export */   translateText: () => (/* binding */ translateText)\n/* harmony export */ });\n// Wordnik API service for fetching phrases and definitions\nconst WORDNIK_API_KEY = process.env.WORDNIK_API_KEY;\nconst WORDNIK_BASE_URL = \"https://api.wordnik.com/v4\";\n// Common English idioms and phrases to fetch from Wordnik\nconst COMMON_PHRASES = [\n    \"break the ice\",\n    \"hit the books\",\n    \"piece of cake\",\n    \"spill the beans\",\n    \"cost an arm and a leg\",\n    \"bite the bullet\",\n    \"break a leg\",\n    \"call it a day\",\n    \"cut to the chase\",\n    \"get the ball rolling\",\n    \"hang in there\",\n    \"it's raining cats and dogs\",\n    \"kill two birds with one stone\",\n    \"let the cat out of the bag\",\n    \"once in a blue moon\",\n    \"the ball is in your court\",\n    \"under the weather\",\n    \"when pigs fly\",\n    \"you can't judge a book by its cover\",\n    \"a blessing in disguise\",\n    \"better late than never\",\n    \"don't cry over spilled milk\",\n    \"every cloud has a silver lining\",\n    \"go the extra mile\",\n    \"it takes two to tango\",\n    \"keep your chin up\",\n    \"make a long story short\",\n    \"no pain no gain\",\n    \"practice makes perfect\",\n    \"the early bird catches the worm\"\n];\nasync function getRandomPhrase() {\n    console.log(\"Wordnik API Key:\", WORDNIK_API_KEY ? \"Present\" : \"Missing\");\n    if (!WORDNIK_API_KEY) {\n        console.error(\"Wordnik API key not found\");\n        return null;\n    }\n    try {\n        // Try to get a truly random phrase from Wordnik's database\n        const randomPhrase = await getRandomPhraseFromWordnik();\n        if (randomPhrase) {\n            console.log(`Fetching data for random phrase from Wordnik: \"${randomPhrase}\"`);\n            // Fetch definition and examples from Wordnik\n            const [definitions, examples] = await Promise.all([\n                fetchDefinitions(randomPhrase),\n                fetchExamples(randomPhrase)\n            ]);\n            console.log(`Got ${definitions.length} definitions and ${examples.length} examples`);\n            return {\n                phrase: randomPhrase,\n                definitions: definitions || [],\n                examples: examples || []\n            };\n        } else {\n            // Fallback to our curated list if Wordnik random fails\n            console.log(\"Falling back to curated phrase list\");\n            const randomPhrase = COMMON_PHRASES[Math.floor(Math.random() * COMMON_PHRASES.length)];\n            console.log(`Fetching data for curated phrase: \"${randomPhrase}\"`);\n            const [definitions, examples] = await Promise.all([\n                fetchDefinitions(randomPhrase),\n                fetchExamples(randomPhrase)\n            ]);\n            console.log(`Got ${definitions.length} definitions and ${examples.length} examples`);\n            return {\n                phrase: randomPhrase,\n                definitions: definitions || [],\n                examples: examples || []\n            };\n        }\n    } catch (error) {\n        console.error(\"Error fetching phrase from Wordnik:\", error);\n        return null;\n    }\n}\n// Simple function to get a random phrase using a more reliable approach\nasync function getSimpleRandomPhrase() {\n    try {\n        console.log(\"Trying simple random phrase approach...\");\n        // Use a list of common English words that often appear in idioms\n        const commonWords = [\n            \"break\",\n            \"make\",\n            \"take\",\n            \"get\",\n            \"give\",\n            \"put\",\n            \"come\",\n            \"go\",\n            \"see\",\n            \"know\",\n            \"time\",\n            \"way\",\n            \"day\",\n            \"man\",\n            \"thing\",\n            \"life\",\n            \"hand\",\n            \"part\",\n            \"child\",\n            \"eye\",\n            \"woman\",\n            \"place\",\n            \"work\",\n            \"week\",\n            \"case\",\n            \"point\",\n            \"government\",\n            \"company\"\n        ];\n        // Pick a random word\n        const randomWord = commonWords[Math.floor(Math.random() * commonWords.length)];\n        console.log(`Selected random word for phrase search: \"${randomWord}\"`);\n        // Try to get a random word from Wordnik first\n        try {\n            const response = await fetch(`${WORDNIK_BASE_URL}/words.json/randomWord?hasDictionaryDef=true&minCorpusCount=1000&maxCorpusCount=-1&minDictionaryCount=1&maxDictionaryCount=-1&minLength=4&maxLength=12&api_key=${WORDNIK_API_KEY}`, {\n                method: \"GET\",\n                headers: {\n                    \"Accept\": \"application/json\",\n                    \"User-Agent\": \"English-Phrase-Pro/1.0\"\n                }\n            });\n            if (response.ok) {\n                const wordData = await response.json();\n                if (wordData && wordData.word) {\n                    console.log(`Got random word from Wordnik: \"${wordData.word}\"`);\n                    // Look for this word in our curated phrases\n                    const matchingPhrases = COMMON_PHRASES.filter((phrase)=>phrase.toLowerCase().includes(wordData.word.toLowerCase()));\n                    if (matchingPhrases.length > 0) {\n                        const selectedPhrase = matchingPhrases[Math.floor(Math.random() * matchingPhrases.length)];\n                        console.log(`Found curated phrase containing Wordnik word: \"${selectedPhrase}\"`);\n                        return selectedPhrase;\n                    }\n                }\n            }\n        } catch (error) {\n            console.log(\"Wordnik random word failed, using fallback approach\");\n        }\n        // Fallback: use our common words to find phrases\n        const matchingPhrases = COMMON_PHRASES.filter((phrase)=>phrase.toLowerCase().includes(randomWord.toLowerCase()));\n        if (matchingPhrases.length > 0) {\n            const selectedPhrase = matchingPhrases[Math.floor(Math.random() * matchingPhrases.length)];\n            console.log(`Found curated phrase containing common word: \"${selectedPhrase}\"`);\n            return selectedPhrase;\n        }\n        console.log(`No phrases found containing word: \"${randomWord}\"`);\n        return null;\n    } catch (error) {\n        console.error(\"Error in simple random phrase approach:\", error);\n        return null;\n    }\n}\n// Function to get word of the day and find phrases containing it\nasync function getWordOfDayPhrase() {\n    try {\n        console.log(\"Trying word of the day approach...\");\n        const response = await fetch(`${WORDNIK_BASE_URL}/words.json/wordOfTheDay?api_key=${WORDNIK_API_KEY}`, {\n            method: \"GET\",\n            headers: {\n                \"Accept\": \"application/json\",\n                \"User-Agent\": \"English-Phrase-Pro/1.0\"\n            }\n        });\n        if (!response.ok) {\n            console.log(`Failed to fetch word of the day: ${response.status} ${response.statusText}`);\n            return null;\n        }\n        const wordOfDay = await response.json();\n        if (!wordOfDay || !wordOfDay.word) {\n            console.log(\"No word of the day returned\");\n            return null;\n        }\n        console.log(`Word of the day: \"${wordOfDay.word}\"`);\n        // Try to find phrases containing the word of the day\n        const phraseResponse = await fetch(`${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(wordOfDay.word)}/phrases?limit=15&wlmi=0&useCanonical=false&api_key=${WORDNIK_API_KEY}`);\n        if (phraseResponse.ok) {\n            const phrases = await phraseResponse.json();\n            if (phrases && phrases.length > 0) {\n                // Filter for actual meaningful phrases\n                const meaningfulPhrases = phrases.filter((phraseObj)=>{\n                    const phrase = phraseObj.gram1 || phraseObj.gram2 || \"\";\n                    return phrase.includes(\" \") && phrase.length > 8 && phrase.length < 60 && !phrase.includes(\"_\") && /^[a-zA-Z\\s',-]+$/.test(phrase);\n                });\n                if (meaningfulPhrases.length > 0) {\n                    const selectedPhrase = meaningfulPhrases[Math.floor(Math.random() * meaningfulPhrases.length)];\n                    const phrase = selectedPhrase.gram1 || selectedPhrase.gram2;\n                    console.log(`Found phrase with word of the day: \"${phrase}\"`);\n                    return phrase;\n                }\n            }\n        }\n        console.log(`No suitable phrases found for word of the day: \"${wordOfDay.word}\"`);\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching word of the day phrase:\", error);\n        return null;\n    }\n}\n// Function to get phrases from trending words\nasync function getTrendingWordPhrase() {\n    try {\n        console.log(\"Trying trending words approach...\");\n        // Get trending words (this uses a different endpoint)\n        const response = await fetch(`${WORDNIK_BASE_URL}/words.json/randomWords?hasDictionaryDef=true&minCorpusCount=5000&maxCorpusCount=-1&minDictionaryCount=3&maxDictionaryCount=-1&minLength=4&maxLength=10&limit=10&api_key=${WORDNIK_API_KEY}`);\n        if (!response.ok) {\n            console.log(\"Failed to fetch trending words\");\n            return null;\n        }\n        const words = await response.json();\n        if (!words || words.length === 0) {\n            console.log(\"No trending words returned\");\n            return null;\n        }\n        // Try each word to find phrases\n        for (const wordObj of words){\n            const word = wordObj.word;\n            console.log(`Checking trending word: \"${word}\"`);\n            try {\n                const phraseResponse = await fetch(`${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(word)}/phrases?limit=10&wlmi=0&useCanonical=false&api_key=${WORDNIK_API_KEY}`);\n                if (phraseResponse.ok) {\n                    const phrases = await phraseResponse.json();\n                    if (phrases && phrases.length > 0) {\n                        const goodPhrases = phrases.filter((phraseObj)=>{\n                            const phrase = phraseObj.gram1 || phraseObj.gram2 || \"\";\n                            return phrase.includes(\" \") && phrase.length > 6 && phrase.length < 50 && /^[a-zA-Z\\s',-]+$/.test(phrase);\n                        });\n                        if (goodPhrases.length > 0) {\n                            const selectedPhrase = goodPhrases[Math.floor(Math.random() * goodPhrases.length)];\n                            const phrase = selectedPhrase.gram1 || selectedPhrase.gram2;\n                            console.log(`Found trending phrase: \"${phrase}\"`);\n                            return phrase;\n                        }\n                    }\n                }\n            } catch (error) {\n                console.log(`Error checking phrases for word \"${word}\":`, error);\n                continue;\n            }\n        }\n        console.log(\"No suitable phrases found from trending words\");\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching trending word phrases:\", error);\n        return null;\n    }\n}\n// Function to get a truly random phrase from Wordnik's database\nasync function getRandomPhraseFromWordnik() {\n    try {\n        // Strategy 1: Try simple random word approach (most reliable)\n        const simpleRandomPhrase = await getSimpleRandomPhrase();\n        if (simpleRandomPhrase) {\n            return simpleRandomPhrase;\n        }\n        // Strategy 2: Try to get word of the day and find phrases with it\n        const wordOfDayPhrase = await getWordOfDayPhrase();\n        if (wordOfDayPhrase) {\n            return wordOfDayPhrase;\n        }\n        // Strategy 3: Try trending words\n        const trendingPhrase = await getTrendingWordPhrase();\n        if (trendingPhrase) {\n            return trendingPhrase;\n        }\n        // Strategy 4: Get random words and find phrases containing them\n        console.log(\"Trying random words approach...\");\n        const response = await fetch(`${WORDNIK_BASE_URL}/words.json/randomWords?hasDictionaryDef=true&minCorpusCount=1000&maxCorpusCount=-1&minDictionaryCount=1&maxDictionaryCount=-1&minLength=4&maxLength=12&limit=20&api_key=${WORDNIK_API_KEY}`);\n        if (!response.ok) {\n            console.log(\"Failed to fetch random words from Wordnik\");\n            return null;\n        }\n        const words = await response.json();\n        if (!words || words.length === 0) {\n            console.log(\"No random words returned from Wordnik\");\n            return null;\n        }\n        // Filter for words that might be part of common phrases/idioms\n        const potentialPhrases = words.filter((wordObj)=>{\n            const word = wordObj.word.toLowerCase();\n            // Look for words that are commonly part of idioms\n            return word.length >= 4 && !word.includes(\"-\") && /^[a-z]+$/.test(word);\n        });\n        if (potentialPhrases.length === 0) {\n            console.log(\"No suitable phrase candidates found\");\n            return null;\n        }\n        // Pick a random word and try to find phrases containing it\n        const randomWord = potentialPhrases[Math.floor(Math.random() * potentialPhrases.length)];\n        console.log(`Selected random word: \"${randomWord.word}\"`);\n        // Try to find phrases containing this word\n        const phraseResponse = await fetch(`${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(randomWord.word)}/phrases?limit=10&wlmi=0&useCanonical=false&api_key=${WORDNIK_API_KEY}`);\n        if (phraseResponse.ok) {\n            const phrases = await phraseResponse.json();\n            if (phrases && phrases.length > 0) {\n                // Filter for actual phrases (not just compound words)\n                const realPhrases = phrases.filter((phraseObj)=>{\n                    const phrase = phraseObj.gram1 || phraseObj.gram2 || \"\";\n                    return phrase.includes(\" \") && phrase.length > 5 && phrase.length < 50;\n                });\n                if (realPhrases.length > 0) {\n                    const selectedPhrase = realPhrases[Math.floor(Math.random() * realPhrases.length)];\n                    const phrase = selectedPhrase.gram1 || selectedPhrase.gram2;\n                    console.log(`Found phrase containing \"${randomWord.word}\": \"${phrase}\"`);\n                    return phrase;\n                }\n            }\n        }\n        // If no phrases found, try our curated list with the random word\n        const matchingPhrases = COMMON_PHRASES.filter((phrase)=>phrase.toLowerCase().includes(randomWord.word.toLowerCase()));\n        if (matchingPhrases.length > 0) {\n            const selectedPhrase = matchingPhrases[Math.floor(Math.random() * matchingPhrases.length)];\n            console.log(`Using curated phrase containing \"${randomWord.word}\": \"${selectedPhrase}\"`);\n            return selectedPhrase;\n        }\n        console.log(`No phrases found for word \"${randomWord.word}\"`);\n        return null;\n    } catch (error) {\n        console.error(\"Error fetching random phrase from Wordnik:\", error);\n        return null;\n    }\n}\nasync function fetchDefinitions(phrase) {\n    try {\n        const response = await fetch(`${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(phrase)}/definitions?limit=3&includeRelated=false&useCanonical=false&includeTags=false&api_key=${WORDNIK_API_KEY}`);\n        if (!response.ok) {\n            console.log(`No definitions found for \"${phrase}\"`);\n            return [];\n        }\n        const data = await response.json();\n        return data.map((def)=>({\n                text: def.text,\n                partOfSpeech: def.partOfSpeech,\n                source: def.sourceDictionary\n            }));\n    } catch (error) {\n        console.error(`Error fetching definitions for \"${phrase}\":`, error);\n        return [];\n    }\n}\nasync function fetchExamples(phrase) {\n    try {\n        const response = await fetch(`${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(phrase)}/examples?includeDuplicates=false&useCanonical=false&skip=0&limit=5&api_key=${WORDNIK_API_KEY}`);\n        if (!response.ok) {\n            console.log(`No examples found for \"${phrase}\"`);\n            return [];\n        }\n        const data = await response.json();\n        return data.examples?.map((example)=>({\n                text: example.text,\n                title: example.title,\n                url: example.url\n            })) || [];\n    } catch (error) {\n        console.error(`Error fetching examples for \"${phrase}\":`, error);\n        return [];\n    }\n}\n// Function to translate text using Google Translate API\nasync function translateText(text, targetLanguage) {\n    const GOOGLE_API_KEY = process.env.GOOGLE_TRANSLATION_API_KEY;\n    if (!GOOGLE_API_KEY) {\n        console.log(\"Google Translation API key not found, using fallback translation\");\n        return getFallbackTranslation(text, targetLanguage);\n    }\n    try {\n        console.log(`Attempting to translate: \"${text}\" to ${targetLanguage}`);\n        const response = await fetch(`https://translation.googleapis.com/language/translate/v2?key=${GOOGLE_API_KEY}`, {\n            method: \"POST\",\n            headers: {\n                \"Content-Type\": \"application/json\"\n            },\n            body: JSON.stringify({\n                q: text,\n                target: targetLanguage,\n                source: \"en\"\n            })\n        });\n        if (!response.ok) {\n            const errorText = await response.text();\n            console.error(`Translation API error: ${response.status} - ${errorText}`);\n            return getFallbackTranslation(text, targetLanguage);\n        }\n        const data = await response.json();\n        const translatedText = data.data.translations[0].translatedText;\n        console.log(`Translation successful: \"${translatedText}\"`);\n        return translatedText;\n    } catch (error) {\n        console.error(\"Error translating text:\", error);\n        return getFallbackTranslation(text, targetLanguage);\n    }\n}\n// Fallback translation function for when Google Translate is not available\nfunction getFallbackTranslation(text, targetLanguage) {\n    // Basic word-by-word translation patterns\n    const translationDict = {\n        \"am\": {\n            \"Don't blame\": \"አትወቅስ\",\n            \"just him\": \"እሱን ብቻ\",\n            \"for the\": \"ለ\",\n            \"argument\": \"ክርክር\",\n            \"Both companies\": \"ሁለቱም ኩባንያዎች\",\n            \"are responsible\": \"ተጠያቂ ናቸው\",\n            \"failed merger\": \"ያልተሳካ ውህደት\",\n            \"it takes two to tango\": \"ታንጎ ለመጨፍር ሁለት ይወስዳል\"\n        },\n        \"es\": {\n            \"Don't blame\": \"No culpes\",\n            \"just him\": \"solo a \\xe9l\",\n            \"for the\": \"por el\",\n            \"argument\": \"argumento\",\n            \"Both companies\": \"Ambas empresas\",\n            \"are responsible\": \"son responsables\",\n            \"failed merger\": \"fusi\\xf3n fallida\",\n            \"it takes two to tango\": \"se necesitan dos para bailar tango\"\n        }\n    };\n    if (!translationDict[targetLanguage]) {\n        return text; // Return original if language not supported\n    }\n    let translatedText = text;\n    Object.entries(translationDict[targetLanguage]).forEach(([english, translated])=>{\n        translatedText = translatedText.replace(new RegExp(english, \"gi\"), translated);\n    });\n    return translatedText;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./lib/wordnik.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/@babel","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/@panva"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fapi%2Fphrases%2Frandom%2Froute&page=%2Fapi%2Fphrases%2Frandom%2Froute&appPaths=&pagePath=private-next-app-dir%2Fapi%2Fphrases%2Frandom%2Froute.ts&appDir=D%3A%5CSoftware%20Projects%5CLanguage%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSoftware%20Projects%5CLanguage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();