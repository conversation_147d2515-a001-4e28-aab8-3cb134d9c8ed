'use client';

import { useState, useEffect } from 'react';
import { useSession, signOut } from 'next-auth/react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import type { Phrase, ExampleSentence } from '@/types';

export default function Learn() {
  const { data: session, status } = useSession();
  const router = useRouter();
  const [currentPhrase, setCurrentPhrase] = useState<Phrase | null>(null);
  const [loading, setLoading] = useState(true);
  const [showTranslation, setShowTranslation] = useState(false);
  const [showExamples, setShowExamples] = useState(false);
  const [showSynonyms, setShowSynonyms] = useState(false);

  // Sample data for demo mode
  const samplePhrases: Phrase[] = [
    {
      id: '1',
      englishPhrase: 'Break the ice',
      phraseType: 'idiom',
      translations: {
        am: 'በረዶውን መስበር',
        es: 'Romper el hielo',
        zh: '打破僵局',
        fr: '<PERSON><PERSON><PERSON> la glace',
        de: 'Das Eis brechen'
      },
      exampleSentences: [
        {
          english: 'He told a joke to break the ice at the meeting.',
          translated: {
            am: 'በስብሰባው ላይ በረዶውን ለመስበር ቀልድ ተናገረ።',
            es: 'Contó un chiste para romper el hielo en la reunión.',
            zh: '他讲了个笑话来打破会议上的僵局。',
            fr: 'Il a raconté une blague pour briser la glace lors de la réunion.',
            de: 'Er erzählte einen Witz, um das Eis bei der Besprechung zu brechen.'
          }
        }
      ],
      synonyms: ['Start a conversation', 'Make people feel comfortable', 'Ease tension']
    },
    {
      id: '2',
      englishPhrase: 'Hit the books',
      phraseType: 'idiom',
      translations: {
        es: 'Ponerse a estudiar',
        zh: '开始学习',
        fr: 'Se mettre aux études',
        de: 'Die Bücher wälzen'
      },
      exampleSentences: [
        {
          english: 'I need to hit the books for my exam tomorrow.',
          translated: {
            es: 'Necesito ponerme a estudiar para mi examen de mañana.',
            zh: '我需要为明天的考试开始学习。',
            fr: 'Je dois me mettre aux études pour mon examen de demain.',
            de: 'Ich muss die Bücher wälzen für meine Prüfung morgen.'
          }
        }
      ],
      synonyms: ['Study hard', 'Start studying', 'Focus on learning']
    },
    {
      id: '3',
      englishPhrase: 'Piece of cake',
      phraseType: 'idiom',
      translations: {
        es: 'Pan comido',
        zh: '小菜一碟',
        fr: 'Du gâteau',
        de: 'Ein Kinderspiel'
      },
      exampleSentences: [
        {
          english: 'The math test was a piece of cake.',
          translated: {
            es: 'El examen de matemáticas fue pan comido.',
            zh: '数学考试是小菜一碟。',
            fr: 'Le test de maths était du gâteau.',
            de: 'Der Mathe-Test war ein Kinderspiel.'
          }
        }
      ],
      synonyms: ['Very easy', 'Simple task', 'No problem']
    }
  ];

  const [currentPhraseIndex, setCurrentPhraseIndex] = useState(0);
  const [hasInitialized, setHasInitialized] = useState(false);
  const [showLanguageModal, setShowLanguageModal] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState(session?.user?.preferredLanguage || 'am');

  // Prevent auto-refresh when returning to the page
  useEffect(() => {
    const handleVisibilityChange = () => {
      // Do nothing when page becomes visible again
      // This prevents auto-refresh when returning from other apps/tabs
    };

    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  useEffect(() => {
    // Only initialize once when the component mounts or authentication status changes
    if (status === 'loading' || hasInitialized) return;

    if (status === 'unauthenticated') {
      // In demo mode, show sample phrases
      setCurrentPhrase(samplePhrases[currentPhraseIndex]);
      setLoading(false);
      setHasInitialized(true);
    } else if (status === 'authenticated' && !session?.user?.preferredLanguage) {
      router.push('/onboarding');
    } else if (status === 'authenticated') {
      fetchRandomPhrase();
      setHasInitialized(true);
    }
  }, [status, session, router]); // Removed currentPhraseIndex from dependencies

  const fetchRandomPhrase = async () => {
    setLoading(true);
    try {
      if (status === 'unauthenticated') {
        // Demo mode - use sample data
        const randomIndex = Math.floor(Math.random() * samplePhrases.length);
        setCurrentPhraseIndex(randomIndex);
        setCurrentPhrase(samplePhrases[randomIndex]);
      } else {
        // Authenticated mode - fetch from API
        const response = await fetch('/api/phrases/random');
        if (!response.ok) throw new Error('Failed to fetch phrase');
        const data = await response.json();
        setCurrentPhrase(data);
      }
      // Reset states
      setShowTranslation(false);
      setShowExamples(false);
      setShowSynonyms(false);
    } catch (error) {
      console.error('Error fetching phrase:', error);
      // Fallback to demo data if API fails
      if (samplePhrases.length > 0) {
        const randomIndex = Math.floor(Math.random() * samplePhrases.length);
        setCurrentPhraseIndex(randomIndex);
        setCurrentPhrase(samplePhrases[randomIndex]);
      }
    } finally {
      setLoading(false);
    }
  };

  const handleKnowPhrase = async () => {
    if (!currentPhrase) return;

    if (status === 'unauthenticated') {
      // Demo mode - just fetch a new phrase
      fetchRandomPhrase();
      return;
    }

    try {
      await fetch('/api/phrases/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phraseId: currentPhrase.id,
          status: 'known',
          phraseData: {
            englishPhrase: currentPhrase.englishPhrase,
            phraseType: currentPhrase.phraseType,
            translations: currentPhrase.translations,
            exampleSentences: currentPhrase.exampleSentences,
            synonyms: currentPhrase.synonyms
          }
        }),
      });

      // Fetch a new phrase
      fetchRandomPhrase();
    } catch (error) {
      console.error('Error marking phrase as known:', error);
      // Fallback to demo mode
      fetchRandomPhrase();
    }
  };

  const handleDontKnowPhrase = async () => {
    if (!currentPhrase) return;

    if (status === 'unauthenticated') {
      // Demo mode - just show translation
      setShowTranslation(true);
      return;
    }

    try {
      await fetch('/api/phrases/status', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          phraseId: currentPhrase.id,
          status: 'learning',
        }),
      });

      // Show translation
      setShowTranslation(true);
    } catch (error) {
      console.error('Error marking phrase as learning:', error);
      // Fallback to demo mode
      setShowTranslation(true);
    }
  };

  const handleLanguageChange = async (newLanguage: string) => {
    try {
      // Update user preference in database
      const response = await fetch('/api/user/preferences', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          preferredLanguage: newLanguage,
        }),
      });

      if (response.ok) {
        setSelectedLanguage(newLanguage);
        setShowLanguageModal(false);
        // Refresh the page to load phrases in new language
        window.location.reload();
      } else {
        console.error('Failed to update language preference');
      }
    } catch (error) {
      console.error('Error updating language preference:', error);
    }
  };

  const handleLogout = async () => {
    await signOut({ callbackUrl: '/' });
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!currentPhrase) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen p-4">
        <div className="w-full max-w-md p-6 bg-white rounded-lg shadow-md text-center">
          <h2 className="text-xl font-semibold mb-4">No phrases available</h2>
          <p className="mb-4">We couldn't find any phrases for you to learn right now.</p>
          <button
            onClick={() => window.location.reload()}
            className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors"
          >
            Refresh
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-4 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900">
      {/* Header */}
      <div className="max-w-4xl mx-auto mb-8">
        <div className="flex items-center justify-between mb-4">
          <Link
            href="/"
            className="inline-flex items-center gap-2 text-slate-600 hover:text-slate-800 dark:text-slate-200 dark:hover:text-white transition-colors"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 19l-7-7 7-7" />
            </svg>
            Home
          </Link>

          {status === 'authenticated' && (
            <div className="flex items-center gap-4">
              <div className="text-sm text-slate-600 dark:text-slate-200">
                Welcome back, {session?.user?.name?.split(' ')[0]}! 👋
              </div>

              {/* Language Preference Button */}
              <button
                onClick={() => setShowLanguageModal(true)}
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/50 dark:hover:bg-blue-800/50 text-blue-700 dark:text-blue-300 rounded-lg transition-colors"
                title="Change language preference"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                </svg>
                {session?.user?.preferredLanguage === 'am' ? 'አማርኛ' : 'Español'}
              </button>

              {/* Logout Button */}
              <button
                onClick={handleLogout}
                className="inline-flex items-center gap-2 px-3 py-1.5 text-sm bg-red-100 hover:bg-red-200 dark:bg-red-900/50 dark:hover:bg-red-800/50 text-red-700 dark:text-red-300 rounded-lg transition-colors"
                title="Sign out"
              >
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1" />
                </svg>
                Logout
              </button>
            </div>
          )}
        </div>

        {status === 'unauthenticated' && (
          <div className="glass-card p-4 mb-6 animate-slide-up dark:bg-slate-800/80 dark:border-slate-600">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center">
                <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              </div>
              <div>
                <p className="font-semibold text-slate-800 dark:text-white">Demo Mode</p>
                <p className="text-sm text-slate-600 dark:text-slate-200">You're viewing sample phrases. <Link href="/auth/signin" className="text-blue-600 hover:text-blue-700 dark:text-blue-300 dark:hover:text-blue-200 underline">Sign in</Link> for full functionality.</p>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Main Learning Area */}
      <div className="flex flex-col items-center justify-center">
        <div className="w-full max-w-2xl card p-8 animate-slide-up dark:bg-slate-800/90 dark:border-slate-600 dark:shadow-2xl">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg mb-4">
              <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
              </svg>
            </div>
            <h1 className="text-3xl font-bold text-slate-800 dark:text-white mb-2">Learn English Phrases</h1>
            <p className="text-slate-600 dark:text-slate-200">Master phrases through interactive learning</p>
          </div>

          {/* Phrase Display */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center gap-3 mb-4">
              <div className="inline-flex items-center gap-2 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-200 px-3 py-1 rounded-full text-sm font-medium">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z" />
                </svg>
                {currentPhrase.phraseType.replace('_', ' ').toUpperCase()}
              </div>
              {status === 'authenticated' && (
                <div className="inline-flex items-center gap-1 bg-green-100 dark:bg-green-800/60 text-green-700 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium">
                  <svg className="w-3 h-3" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                  API
                </div>
              )}
            </div>
            <h2 className="text-4xl font-bold text-slate-800 dark:text-white mb-4 leading-tight">
              "{currentPhrase.englishPhrase}"
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto"></div>
          </div>
        
          {!showTranslation ? (
            <div className="space-y-6">
              <div className="text-center">
                <p className="text-lg text-slate-700 dark:text-slate-200 mb-6">Do you know this phrase?</p>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                  <button
                    onClick={handleKnowPhrase}
                    className="btn-success flex items-center justify-center gap-3 py-4"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                    </svg>
                    Yes, I know it
                  </button>
                  <button
                    onClick={handleDontKnowPhrase}
                    className="btn-primary flex items-center justify-center gap-3 py-4"
                  >
                    <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                    </svg>
                    No, show me
                  </button>
                </div>
              </div>
            </div>
          ) : (
            <div className="space-y-8 animate-fade-in">
              {/* Translation Card */}
              <div className="glass-card p-6 border-l-4 border-blue-500">
                <div className="flex items-center gap-3 mb-4">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center">
                    <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
                    </svg>
                  </div>
                  <h3 className="text-xl font-semibold text-slate-800">Translation</h3>
                </div>
                <p className="text-lg text-slate-700 leading-relaxed">
                  {currentPhrase.translations[session?.user?.preferredLanguage || 'es']}
                </p>
                {status === 'unauthenticated' && (
                  <div className="mt-4 p-3 bg-slate-50 rounded-lg">
                    <p className="text-sm text-slate-600">
                      <span className="font-medium">Available languages:</span> Spanish, Chinese, French, German
                    </p>
                  </div>
                )}
              </div>

              {/* Examples Section */}
              <div className="glass-card overflow-hidden">
                <button
                  onClick={() => setShowExamples(!showExamples)}
                  className="w-full flex justify-between items-center p-6 hover:bg-slate-50 transition-colors group"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                      </svg>
                    </div>
                    <span className="text-lg font-semibold text-slate-800">Example Sentences</span>
                  </div>
                  <div className={`w-8 h-8 rounded-full bg-slate-100 flex items-center justify-center transition-transform duration-200 ${showExamples ? 'rotate-180' : ''}`}>
                    <svg className="w-4 h-4 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </button>

                {showExamples && (
                  <div className="px-6 pb-6 space-y-4 animate-slide-up">
                    {currentPhrase.exampleSentences.map((example: ExampleSentence, index: number) => (
                      <div key={index} className="bg-white/50 border border-slate-200 rounded-xl p-4 hover:bg-white/80 transition-colors">
                        <p className="text-slate-800 font-medium mb-2 leading-relaxed">"{example.english}"</p>
                        <p className="text-slate-600 text-sm leading-relaxed">
                          {example.translated[session?.user?.preferredLanguage || 'es']}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </div>

              {/* Synonyms Section */}
              <div className="glass-card overflow-hidden">
                <button
                  onClick={() => setShowSynonyms(!showSynonyms)}
                  className="w-full flex justify-between items-center p-6 hover:bg-slate-50 transition-colors group"
                >
                  <div className="flex items-center gap-3">
                    <div className="w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center">
                      <svg className="w-5 h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z" />
                      </svg>
                    </div>
                    <span className="text-lg font-semibold text-slate-800">Similar Phrases</span>
                  </div>
                  <div className={`w-8 h-8 rounded-full bg-slate-100 flex items-center justify-center transition-transform duration-200 ${showSynonyms ? 'rotate-180' : ''}`}>
                    <svg className="w-4 h-4 text-slate-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 9l-7 7-7-7" />
                    </svg>
                  </div>
                </button>

                {showSynonyms && (
                  <div className="px-6 pb-6 animate-slide-up">
                    <div className="grid grid-cols-1 gap-3">
                      {currentPhrase.synonyms.map((synonym: string, index: number) => (
                        <div key={index} className="bg-white/50 border border-slate-200 rounded-xl p-4 hover:bg-white/80 transition-colors">
                          <p className="text-slate-700 font-medium">"{synonym}"</p>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>

              {/* Action Buttons */}
              <div className="flex flex-col sm:flex-row gap-4 pt-6 border-t border-slate-200 dark:border-slate-600">
                <button
                  onClick={() => fetchRandomPhrase()}
                  className="btn-secondary flex items-center justify-center gap-3 flex-1"
                  title="Skip this phrase and get a new one"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                  </svg>
                  Next Phrase
                </button>

                <button
                  onClick={async () => {
                    if (!currentPhrase) return;

                    if (status === 'unauthenticated') {
                      fetchRandomPhrase();
                      return;
                    }

                    try {
                      await fetch('/api/phrases/status', {
                        method: 'POST',
                        headers: {
                          'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({
                          phraseId: currentPhrase.id,
                          status: 'learning',
                          phraseData: {
                            englishPhrase: currentPhrase.englishPhrase,
                            phraseType: currentPhrase.phraseType,
                            translations: currentPhrase.translations,
                            exampleSentences: currentPhrase.exampleSentences,
                            synonyms: currentPhrase.synonyms
                          }
                        }),
                      });

                      fetchRandomPhrase();
                    } catch (error) {
                      console.error('Error marking for review:', error);
                      fetchRandomPhrase();
                    }
                  }}
                  className="btn-primary flex items-center justify-center gap-3 flex-1"
                  title="Save this phrase to review later - it will appear again in future sessions"
                >
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z" />
                  </svg>
                  Save for Later
                </button>
              </div>

              {/* Help Text */}
              <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg">
                <p className="text-sm text-blue-700 dark:text-blue-200">
                  <strong>💡 How it works:</strong> "Yes, I know it" marks the phrase as learned (won't appear again).
                  "Save for Later" keeps it in your review queue for future practice.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Language Preference Modal */}
      {showLanguageModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md mx-4 shadow-xl">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-slate-800 dark:text-white">
                Choose Your Language
              </h3>
              <button
                onClick={() => setShowLanguageModal(false)}
                className="text-slate-400 hover:text-slate-600 dark:text-slate-300 dark:hover:text-slate-100"
              >
                <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </button>
            </div>

            <p className="text-slate-600 dark:text-slate-300 mb-6">
              Select your preferred language for learning English phrases:
            </p>

            <div className="space-y-3">
              <button
                onClick={() => handleLanguageChange('am')}
                className={`w-full flex items-center gap-3 p-4 rounded-lg border-2 transition-colors ${
                  selectedLanguage === 'am'
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500 text-slate-700 dark:text-slate-300'
                }`}
              >
                <div className="text-2xl">🇪🇹</div>
                <div className="text-left">
                  <div className="font-semibold">አማርኛ (Amharic)</div>
                  <div className="text-sm opacity-75">Learn English phrases in Amharic</div>
                </div>
                {selectedLanguage === 'am' && (
                  <svg className="w-5 h-5 ml-auto text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </button>

              <button
                onClick={() => handleLanguageChange('es')}
                className={`w-full flex items-center gap-3 p-4 rounded-lg border-2 transition-colors ${
                  selectedLanguage === 'es'
                    ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300'
                    : 'border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500 text-slate-700 dark:text-slate-300'
                }`}
              >
                <div className="text-2xl">🇪🇸</div>
                <div className="text-left">
                  <div className="font-semibold">Español (Spanish)</div>
                  <div className="text-sm opacity-75">Learn English phrases in Spanish</div>
                </div>
                {selectedLanguage === 'es' && (
                  <svg className="w-5 h-5 ml-auto text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                  </svg>
                )}
              </button>
            </div>

            <div className="mt-6 flex gap-3">
              <button
                onClick={() => setShowLanguageModal(false)}
                className="flex-1 px-4 py-2 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors"
              >
                Cancel
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}