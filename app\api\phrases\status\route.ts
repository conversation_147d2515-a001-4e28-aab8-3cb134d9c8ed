import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const requestBody = await request.json();
    const { phraseId, status, phraseData } = requestBody;

    if (!phraseId || !status) {
      return NextResponse.json({ error: "Missing required fields" }, { status: 400 });
    }
    console.log(`Saving phrase status: User ${session.user.id}, Phrase ${phraseId}, Status ${status}`);

    // First, ensure the user exists in the database
    await prisma.user.upsert({
      where: { id: session.user.id! },
      update: {
        name: session.user.name,
        email: session.user.email!,
        image: session.user.image,
        preferredLanguage: session.user.preferredLanguage
      },
      create: {
        id: session.user.id!,
        name: session.user.name,
        email: session.user.email!,
        image: session.user.image,
        preferredLanguage: session.user.preferredLanguage
      }
    });

    // Then, ensure the phrase exists in the database
    let phrase = await prisma.phrase.findUnique({
      where: { id: phraseId }
    });

    // If phrase doesn't exist, create it (for dynamic phrases from Wordnik)
    if (!phrase) {
      // Use the phrase data sent from the frontend, or create minimal record
      const phraseInfo = phraseData || {
        englishPhrase: `Dynamic phrase ${phraseId}`,
        phraseType: 'idiom',
        translations: {},
        exampleSentences: [],
        synonyms: []
      };

      phrase = await prisma.phrase.create({
        data: {
          id: phraseId,
          englishPhrase: phraseInfo.englishPhrase,
          phraseType: phraseInfo.phraseType,
          translations: phraseInfo.translations,
          exampleSentences: phraseInfo.exampleSentences,
          synonyms: phraseInfo.synonyms || []
        }
      });
      console.log(`Created new phrase record: ${phraseId} - "${phraseInfo.englishPhrase}"`);
    }

    // Upsert the user phrase status
    const userPhrase = await prisma.userPhrase.upsert({
      where: {
        userId_phraseId: {
          userId: session.user.id!,
          phraseId: phraseId
        }
      },
      update: {
        status: status,
        lastReviewed: new Date(),
        masteryLevel: status === 'known' ? 1 : 0
      },
      create: {
        userId: session.user.id!,
        phraseId: phraseId,
        status: status,
        masteryLevel: status === 'known' ? 1 : 0,
        lastReviewed: new Date()
      }
    });

    console.log(`Successfully saved phrase status: ${JSON.stringify(userPhrase)}`);
    return NextResponse.json(userPhrase);
  } catch (error) {
    console.error("Error updating phrase status:", error);
    return NextResponse.json({ error: "Failed to update status" }, { status: 500 });
  }
}