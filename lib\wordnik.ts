// Wordnik API service for fetching phrases and definitions

const WORDNIK_API_KEY = process.env.WORDNIK_API_KEY;
const WORDNIK_BASE_URL = 'https://api.wordnik.com/v4';

// Common English idioms and phrases to fetch from Wordnik
const COMMON_PHRASES = [
  'break the ice',
  'hit the books',
  'piece of cake',
  'spill the beans',
  'cost an arm and a leg',
  'bite the bullet',
  'break a leg',
  'call it a day',
  'cut to the chase',
  'get the ball rolling',
  'hang in there',
  'it\'s raining cats and dogs',
  'kill two birds with one stone',
  'let the cat out of the bag',
  'once in a blue moon',
  'the ball is in your court',
  'under the weather',
  'when pigs fly',
  'you can\'t judge a book by its cover',
  'a blessing in disguise',
  'better late than never',
  'don\'t cry over spilled milk',
  'every cloud has a silver lining',
  'go the extra mile',
  'it takes two to tango',
  'keep your chin up',
  'make a long story short',
  'no pain no gain',
  'practice makes perfect',
  'the early bird catches the worm'
];

export interface WordnikDefinition {
  text: string;
  partOfSpeech?: string;
  source?: string;
}

export interface WordnikExample {
  text: string;
  title?: string;
  url?: string;
}

export interface WordnikPhrase {
  phrase: string;
  definitions: WordnikDefinition[];
  examples: WordnikExample[];
  pronunciations?: any[];
}

export async function getRandomPhrase(): Promise<WordnikPhrase | null> {
  console.log('Wordnik API Key:', WORDNIK_API_KEY ? 'Present' : 'Missing');

  if (!WORDNIK_API_KEY) {
    console.error('Wordnik API key not found');
    return null;
  }

  try {
    // Try to get a truly random phrase from Wordnik's database
    const randomPhrase = await getRandomPhraseFromWordnik();

    if (randomPhrase) {
      console.log(`Fetching data for random phrase from Wordnik: "${randomPhrase}"`);

      // Fetch definition and examples from Wordnik
      const [definitions, examples] = await Promise.all([
        fetchDefinitions(randomPhrase),
        fetchExamples(randomPhrase)
      ]);

      console.log(`Got ${definitions.length} definitions and ${examples.length} examples`);

      return {
        phrase: randomPhrase,
        definitions: definitions || [],
        examples: examples || []
      };
    } else {
      // Fallback to our curated list if Wordnik random fails
      console.log('Falling back to curated phrase list');
      const randomPhrase = COMMON_PHRASES[Math.floor(Math.random() * COMMON_PHRASES.length)];
      console.log(`Fetching data for curated phrase: "${randomPhrase}"`);

      const [definitions, examples] = await Promise.all([
        fetchDefinitions(randomPhrase),
        fetchExamples(randomPhrase)
      ]);

      console.log(`Got ${definitions.length} definitions and ${examples.length} examples`);

      return {
        phrase: randomPhrase,
        definitions: definitions || [],
        examples: examples || []
      };
    }
  } catch (error) {
    console.error('Error fetching phrase from Wordnik:', error);
    return null;
  }
}

// Simple function to get a random phrase using a more reliable approach
async function getSimpleRandomPhrase(): Promise<string | null> {
  try {
    console.log('Trying simple random phrase approach...');

    // Use a list of common English words that often appear in idioms
    const commonWords = [
      'break', 'make', 'take', 'get', 'give', 'put', 'come', 'go', 'see', 'know',
      'time', 'way', 'day', 'man', 'thing', 'life', 'hand', 'part', 'child', 'eye',
      'woman', 'place', 'work', 'week', 'case', 'point', 'government', 'company'
    ];

    // Pick a random word
    const randomWord = commonWords[Math.floor(Math.random() * commonWords.length)];
    console.log(`Selected random word for phrase search: "${randomWord}"`);

    // Try to get a random word from Wordnik first
    try {
      const response = await fetch(
        `${WORDNIK_BASE_URL}/words.json/randomWord?hasDictionaryDef=true&minCorpusCount=1000&maxCorpusCount=-1&minDictionaryCount=1&maxDictionaryCount=-1&minLength=4&maxLength=12&api_key=${WORDNIK_API_KEY}`,
        {
          method: 'GET',
          headers: {
            'Accept': 'application/json',
            'User-Agent': 'English-Phrase-Pro/1.0'
          }
        }
      );

      if (response.ok) {
        const wordData = await response.json();
        if (wordData && wordData.word) {
          console.log(`Got random word from Wordnik: "${wordData.word}"`);

          // Look for this word in our curated phrases
          const matchingPhrases = COMMON_PHRASES.filter(phrase =>
            phrase.toLowerCase().includes(wordData.word.toLowerCase())
          );

          if (matchingPhrases.length > 0) {
            const selectedPhrase = matchingPhrases[Math.floor(Math.random() * matchingPhrases.length)];
            console.log(`Found curated phrase containing Wordnik word: "${selectedPhrase}"`);
            return selectedPhrase;
          }
        }
      }
    } catch (error) {
      console.log('Wordnik random word failed, using fallback approach');
    }

    // Fallback: use our common words to find phrases
    const matchingPhrases = COMMON_PHRASES.filter(phrase =>
      phrase.toLowerCase().includes(randomWord.toLowerCase())
    );

    if (matchingPhrases.length > 0) {
      const selectedPhrase = matchingPhrases[Math.floor(Math.random() * matchingPhrases.length)];
      console.log(`Found curated phrase containing common word: "${selectedPhrase}"`);
      return selectedPhrase;
    }

    console.log(`No phrases found containing word: "${randomWord}"`);
    return null;

  } catch (error) {
    console.error('Error in simple random phrase approach:', error);
    return null;
  }
}

// Function to get word of the day and find phrases containing it
async function getWordOfDayPhrase(): Promise<string | null> {
  try {
    console.log('Trying word of the day approach...');
    const response = await fetch(
      `${WORDNIK_BASE_URL}/words.json/wordOfTheDay?api_key=${WORDNIK_API_KEY}`,
      {
        method: 'GET',
        headers: {
          'Accept': 'application/json',
          'User-Agent': 'English-Phrase-Pro/1.0'
        }
      }
    );

    if (!response.ok) {
      console.log(`Failed to fetch word of the day: ${response.status} ${response.statusText}`);
      return null;
    }

    const wordOfDay = await response.json();
    if (!wordOfDay || !wordOfDay.word) {
      console.log('No word of the day returned');
      return null;
    }

    console.log(`Word of the day: "${wordOfDay.word}"`);

    // Try to find phrases containing the word of the day
    const phraseResponse = await fetch(
      `${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(wordOfDay.word)}/phrases?limit=15&wlmi=0&useCanonical=false&api_key=${WORDNIK_API_KEY}`
    );

    if (phraseResponse.ok) {
      const phrases = await phraseResponse.json();
      if (phrases && phrases.length > 0) {
        // Filter for actual meaningful phrases
        const meaningfulPhrases = phrases.filter((phraseObj: any) => {
          const phrase = phraseObj.gram1 || phraseObj.gram2 || '';
          return phrase.includes(' ') &&
                 phrase.length > 8 &&
                 phrase.length < 60 &&
                 !phrase.includes('_') &&
                 /^[a-zA-Z\s',-]+$/.test(phrase);
        });

        if (meaningfulPhrases.length > 0) {
          const selectedPhrase = meaningfulPhrases[Math.floor(Math.random() * meaningfulPhrases.length)];
          const phrase = selectedPhrase.gram1 || selectedPhrase.gram2;
          console.log(`Found phrase with word of the day: "${phrase}"`);
          return phrase;
        }
      }
    }

    console.log(`No suitable phrases found for word of the day: "${wordOfDay.word}"`);
    return null;

  } catch (error) {
    console.error('Error fetching word of the day phrase:', error);
    return null;
  }
}

// Function to get phrases from trending words
async function getTrendingWordPhrase(): Promise<string | null> {
  try {
    console.log('Trying trending words approach...');

    // Get trending words (this uses a different endpoint)
    const response = await fetch(
      `${WORDNIK_BASE_URL}/words.json/randomWords?hasDictionaryDef=true&minCorpusCount=5000&maxCorpusCount=-1&minDictionaryCount=3&maxDictionaryCount=-1&minLength=4&maxLength=10&limit=10&api_key=${WORDNIK_API_KEY}`
    );

    if (!response.ok) {
      console.log('Failed to fetch trending words');
      return null;
    }

    const words = await response.json();
    if (!words || words.length === 0) {
      console.log('No trending words returned');
      return null;
    }

    // Try each word to find phrases
    for (const wordObj of words) {
      const word = wordObj.word;
      console.log(`Checking trending word: "${word}"`);

      try {
        const phraseResponse = await fetch(
          `${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(word)}/phrases?limit=10&wlmi=0&useCanonical=false&api_key=${WORDNIK_API_KEY}`
        );

        if (phraseResponse.ok) {
          const phrases = await phraseResponse.json();
          if (phrases && phrases.length > 0) {
            const goodPhrases = phrases.filter((phraseObj: any) => {
              const phrase = phraseObj.gram1 || phraseObj.gram2 || '';
              return phrase.includes(' ') &&
                     phrase.length > 6 &&
                     phrase.length < 50 &&
                     /^[a-zA-Z\s',-]+$/.test(phrase);
            });

            if (goodPhrases.length > 0) {
              const selectedPhrase = goodPhrases[Math.floor(Math.random() * goodPhrases.length)];
              const phrase = selectedPhrase.gram1 || selectedPhrase.gram2;
              console.log(`Found trending phrase: "${phrase}"`);
              return phrase;
            }
          }
        }
      } catch (error) {
        console.log(`Error checking phrases for word "${word}":`, error);
        continue;
      }
    }

    console.log('No suitable phrases found from trending words');
    return null;

  } catch (error) {
    console.error('Error fetching trending word phrases:', error);
    return null;
  }
}

// Function to get a truly random phrase from Wordnik's database
async function getRandomPhraseFromWordnik(): Promise<string | null> {
  try {
    // Strategy 1: Try simple random word approach (most reliable)
    const simpleRandomPhrase = await getSimpleRandomPhrase();
    if (simpleRandomPhrase) {
      return simpleRandomPhrase;
    }

    // Strategy 2: Try to get word of the day and find phrases with it
    const wordOfDayPhrase = await getWordOfDayPhrase();
    if (wordOfDayPhrase) {
      return wordOfDayPhrase;
    }

    // Strategy 3: Try trending words
    const trendingPhrase = await getTrendingWordPhrase();
    if (trendingPhrase) {
      return trendingPhrase;
    }

    // Strategy 4: Get random words and find phrases containing them
    console.log('Trying random words approach...');
    const response = await fetch(
      `${WORDNIK_BASE_URL}/words.json/randomWords?hasDictionaryDef=true&minCorpusCount=1000&maxCorpusCount=-1&minDictionaryCount=1&maxDictionaryCount=-1&minLength=4&maxLength=12&limit=20&api_key=${WORDNIK_API_KEY}`
    );

    if (!response.ok) {
      console.log('Failed to fetch random words from Wordnik');
      return null;
    }

    const words = await response.json();

    if (!words || words.length === 0) {
      console.log('No random words returned from Wordnik');
      return null;
    }

    // Filter for words that might be part of common phrases/idioms
    const potentialPhrases = words.filter((wordObj: any) => {
      const word = wordObj.word.toLowerCase();
      // Look for words that are commonly part of idioms
      return word.length >= 4 && !word.includes('-') && /^[a-z]+$/.test(word);
    });

    if (potentialPhrases.length === 0) {
      console.log('No suitable phrase candidates found');
      return null;
    }

    // Pick a random word and try to find phrases containing it
    const randomWord = potentialPhrases[Math.floor(Math.random() * potentialPhrases.length)];
    console.log(`Selected random word: "${randomWord.word}"`);

    // Try to find phrases containing this word
    const phraseResponse = await fetch(
      `${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(randomWord.word)}/phrases?limit=10&wlmi=0&useCanonical=false&api_key=${WORDNIK_API_KEY}`
    );

    if (phraseResponse.ok) {
      const phrases = await phraseResponse.json();
      if (phrases && phrases.length > 0) {
        // Filter for actual phrases (not just compound words)
        const realPhrases = phrases.filter((phraseObj: any) => {
          const phrase = phraseObj.gram1 || phraseObj.gram2 || '';
          return phrase.includes(' ') && phrase.length > 5 && phrase.length < 50;
        });

        if (realPhrases.length > 0) {
          const selectedPhrase = realPhrases[Math.floor(Math.random() * realPhrases.length)];
          const phrase = selectedPhrase.gram1 || selectedPhrase.gram2;
          console.log(`Found phrase containing "${randomWord.word}": "${phrase}"`);
          return phrase;
        }
      }
    }

    // If no phrases found, try our curated list with the random word
    const matchingPhrases = COMMON_PHRASES.filter(phrase =>
      phrase.toLowerCase().includes(randomWord.word.toLowerCase())
    );

    if (matchingPhrases.length > 0) {
      const selectedPhrase = matchingPhrases[Math.floor(Math.random() * matchingPhrases.length)];
      console.log(`Using curated phrase containing "${randomWord.word}": "${selectedPhrase}"`);
      return selectedPhrase;
    }

    console.log(`No phrases found for word "${randomWord.word}"`);
    return null;

  } catch (error) {
    console.error('Error fetching random phrase from Wordnik:', error);
    return null;
  }
}

async function fetchDefinitions(phrase: string): Promise<WordnikDefinition[]> {
  try {
    const response = await fetch(
      `${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(phrase)}/definitions?limit=3&includeRelated=false&useCanonical=false&includeTags=false&api_key=${WORDNIK_API_KEY}`
    );
    
    if (!response.ok) {
      console.log(`No definitions found for "${phrase}"`);
      return [];
    }
    
    const data = await response.json();
    return data.map((def: any) => ({
      text: def.text,
      partOfSpeech: def.partOfSpeech,
      source: def.sourceDictionary
    }));
  } catch (error) {
    console.error(`Error fetching definitions for "${phrase}":`, error);
    return [];
  }
}

async function fetchExamples(phrase: string): Promise<WordnikExample[]> {
  try {
    const response = await fetch(
      `${WORDNIK_BASE_URL}/word.json/${encodeURIComponent(phrase)}/examples?includeDuplicates=false&useCanonical=false&skip=0&limit=5&api_key=${WORDNIK_API_KEY}`
    );
    
    if (!response.ok) {
      console.log(`No examples found for "${phrase}"`);
      return [];
    }
    
    const data = await response.json();
    return data.examples?.map((example: any) => ({
      text: example.text,
      title: example.title,
      url: example.url
    })) || [];
  } catch (error) {
    console.error(`Error fetching examples for "${phrase}":`, error);
    return [];
  }
}

// Function to translate text using Google Translate API
export async function translateText(text: string, targetLanguage: string): Promise<string> {
  const GOOGLE_API_KEY = process.env.GOOGLE_TRANSLATION_API_KEY;

  if (!GOOGLE_API_KEY) {
    console.log('Google Translation API key not found, using fallback translation');
    return getFallbackTranslation(text, targetLanguage);
  }

  try {
    console.log(`Attempting to translate: "${text}" to ${targetLanguage}`);

    const response = await fetch(
      `https://translation.googleapis.com/language/translate/v2?key=${GOOGLE_API_KEY}`,
      {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          q: text,
          target: targetLanguage,
          source: 'en'
        })
      }
    );

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`Translation API error: ${response.status} - ${errorText}`);
      return getFallbackTranslation(text, targetLanguage);
    }

    const data = await response.json();
    const translatedText = data.data.translations[0].translatedText;
    console.log(`Translation successful: "${translatedText}"`);
    return translatedText;
  } catch (error) {
    console.error('Error translating text:', error);
    return getFallbackTranslation(text, targetLanguage);
  }
}

// Fallback translation function for when Google Translate is not available
function getFallbackTranslation(text: string, targetLanguage: string): string {
  // Basic word-by-word translation patterns
  const translationDict: Record<string, Record<string, string>> = {
    "am": {
      "Don't blame": "አትወቅስ",
      "just him": "እሱን ብቻ",
      "for the": "ለ",
      "argument": "ክርክር",
      "Both companies": "ሁለቱም ኩባንያዎች",
      "are responsible": "ተጠያቂ ናቸው",
      "failed merger": "ያልተሳካ ውህደት",
      "it takes two to tango": "ታንጎ ለመጨፍር ሁለት ይወስዳል"
    },
    "es": {
      "Don't blame": "No culpes",
      "just him": "solo a él",
      "for the": "por el",
      "argument": "argumento",
      "Both companies": "Ambas empresas",
      "are responsible": "son responsables",
      "failed merger": "fusión fallida",
      "it takes two to tango": "se necesitan dos para bailar tango"
    }
  };

  if (!translationDict[targetLanguage]) {
    return text; // Return original if language not supported
  }

  let translatedText = text;
  Object.entries(translationDict[targetLanguage]).forEach(([english, translated]) => {
    translatedText = translatedText.replace(new RegExp(english, 'gi'), translated);
  });

  return translatedText;
}
