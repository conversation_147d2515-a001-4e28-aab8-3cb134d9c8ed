import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { PrismaClient } from "@prisma/client";

const prisma = new PrismaClient();

export async function POST(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { preferredLanguage } = await request.json();

    if (!preferredLanguage || !['am', 'es'].includes(preferredLanguage)) {
      return NextResponse.json({ error: "Invalid language preference" }, { status: 400 });
    }

    console.log(`Updating language preference for user ${session.user.id}: ${preferredLanguage}`);

    // Update user's language preference in database
    const updatedUser = await prisma.user.update({
      where: { id: session.user.id! },
      data: { preferredLanguage }
    });

    console.log(`Successfully updated language preference: ${JSON.stringify(updatedUser)}`);
    
    // Also update the session (this will be reflected on next session refresh)
    if (session.user) {
      session.user.preferredLanguage = preferredLanguage;
    }

    await prisma.$disconnect();
    return NextResponse.json({ success: true, preferredLanguage });
  } catch (error) {
    console.error("Error updating user preferences:", error);
    return NextResponse.json({ error: "Failed to update preferences" }, { status: 500 });
  }
}
