import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { getRandomPhrase, translateText } from "@/lib/wordnik";
import { PrismaClient } from "@prisma/client";

// Pre-defined translations for common English idioms
function getPhraseTranslations(phrase: string): Record<string, string> {
  const translations: Record<string, Record<string, string>> = {
    "break the ice": {
      "es": "romper el hielo",
      "fr": "briser la glace",
      "de": "das Eis brechen",
      "am": "በረዶውን መስበር"
    },
    "hit the books": {
      "es": "estudiar mucho",
      "fr": "étudier dur",
      "de": "büffeln",
      "am": "መጽሐፍትን መምታት"
    },
    "piece of cake": {
      "es": "pan comido",
      "fr": "du gâteau",
      "de": "ein Kinderspiel",
      "am": "የኬክ ቁራጭ"
    },
    "spill the beans": {
      "es": "revelar el secreto",
      "fr": "vendre la mèche",
      "de": "die Bohnen verschütten",
      "am": "ባቄላውን መፍሰስ"
    },
    "cost an arm and a leg": {
      "es": "costar un ojo de la cara",
      "fr": "coûter les yeux de la tête",
      "de": "ein Vermögen kosten",
      "am": "እጅና እግር ማስወጣት"
    },
    "bite the bullet": {
      "es": "apretar los dientes",
      "fr": "serrer les dents",
      "de": "in den sauren Apfel beißen",
      "am": "ጥይቱን መንከስ"
    },
    "break a leg": {
      "es": "que tengas suerte",
      "fr": "bonne chance",
      "de": "Hals- und Beinbruch",
      "am": "እግርህን ስበር"
    },
    "call it a day": {
      "es": "dar por terminado",
      "fr": "s'arrêter là",
      "de": "Feierabend machen",
      "am": "ቀን ብለን መጥራት"
    },
    "better late than never": {
      "es": "más vale tarde que nunca",
      "fr": "mieux vaut tard que jamais",
      "de": "besser spät als nie",
      "am": "ከአልመጣም ዘግይቶ መምጣት ይሻላል"
    },
    "when pigs fly": {
      "es": "cuando las ranas críen pelo",
      "fr": "quand les poules auront des dents",
      "de": "wenn Schweine fliegen",
      "am": "አሳማዎች በሚበሩበት ጊዜ"
    },
    "don't cry over spilled milk": {
      "es": "no llores sobre la leche derramada",
      "fr": "ne pleure pas sur le lait renversé",
      "de": "weine nicht über verschüttete Milch",
      "am": "በተፈሰሰ ወተት ላይ አትልቅ"
    },
    "cut to the chase": {
      "es": "ir al grano",
      "fr": "aller droit au but",
      "de": "zur Sache kommen",
      "am": "ወደ ዋናው ጉዳይ መሄድ"
    },
    "get the ball rolling": {
      "es": "poner la pelota en movimiento",
      "fr": "faire bouger les choses",
      "de": "den Ball ins Rollen bringen",
      "am": "ኳሱን ማንከባለል"
    },
    "hang in there": {
      "es": "aguanta",
      "fr": "tiens bon",
      "de": "halte durch",
      "am": "እዚያ ተንጠልጥል"
    },
    "it's raining cats and dogs": {
      "es": "llueve a cántaros",
      "fr": "il pleut des cordes",
      "de": "es regnet Bindfäden",
      "am": "ድመቶችና ውሾች እየዘነቡ ነው"
    },
    "kill two birds with one stone": {
      "es": "matar dos pájaros de un tiro",
      "fr": "faire d'une pierre deux coups",
      "de": "zwei Fliegen mit einer Klappe schlagen",
      "am": "በአንድ ድንጋይ ሁለት ወፎችን መግደል"
    },
    "let the cat out of the bag": {
      "es": "dejar salir el gato de la bolsa",
      "fr": "vendre la mèche",
      "de": "die Katze aus dem Sack lassen",
      "am": "ድመቱን ከቦርሳው ውስጥ መልቀቅ"
    },
    "once in a blue moon": {
      "es": "una vez cada muerte de obispo",
      "fr": "tous les trente-six du mois",
      "de": "alle Jubeljahre",
      "am": "በሰማያዊ ጨረቃ ጊዜ"
    },
    "the ball is in your court": {
      "es": "la pelota está en tu cancha",
      "fr": "la balle est dans ton camp",
      "de": "der Ball liegt bei dir",
      "am": "ኳሱ በአንተ ሜዳ ላይ ነው"
    },
    "under the weather": {
      "es": "indispuesto",
      "fr": "pas dans son assiette",
      "de": "nicht auf der Höhe",
      "am": "ከአየር ሁኔታ በታች"
    },
    "a blessing in disguise": {
      "es": "no hay mal que por bien no venga",
      "fr": "à quelque chose malheur est bon",
      "de": "ein Glück im Unglück",
      "am": "በመሸፈኛ ውስጥ ያለ በረከት"
    },
    "every cloud has a silver lining": {
      "es": "no hay mal que por bien no venga",
      "fr": "après la pluie, le beau temps",
      "de": "nach Regen kommt Sonnenschein",
      "am": "እያንዳንዱ ደመና የብር መስመር አለው"
    },
    "go the extra mile": {
      "es": "hacer un esfuerzo adicional",
      "fr": "faire un effort supplémentaire",
      "de": "eine Extrameile gehen",
      "am": "ተጨማሪ ማይል መሄድ"
    },
    "it takes two to tango": {
      "es": "se necesitan dos para bailar tango",
      "fr": "il faut être deux pour danser",
      "de": "zum Tango gehören zwei",
      "am": "ታንጎ ለመጨፍር ሁለት ይወስዳል"
    },
    "keep your chin up": {
      "es": "mantén la cabeza alta",
      "fr": "garde le moral",
      "de": "lass den Kopf nicht hängen",
      "am": "አገናህን ወደ ላይ አቆይ"
    },
    "make a long story short": {
      "es": "para resumir",
      "fr": "pour faire court",
      "de": "um es kurz zu machen",
      "am": "ረጅም ታሪክን አጭር ማድረግ"
    },
    "no pain no gain": {
      "es": "sin dolor no hay ganancia",
      "fr": "on n'a rien sans rien",
      "de": "ohne Fleiß kein Preis",
      "am": "ህመም ከሌለ ትርፍ የለም"
    },
    "practice makes perfect": {
      "es": "la práctica hace al maestro",
      "fr": "c'est en forgeant qu'on devient forgeron",
      "de": "Übung macht den Meister",
      "am": "ልምምድ ፍጹምነት ያመጣል"
    },
    "the early bird catches the worm": {
      "es": "al que madruga, Dios le ayuda",
      "fr": "l'avenir appartient à ceux qui se lèvent tôt",
      "de": "der frühe Vogel fängt den Wurm",
      "am": "ቀደም ያለ ወፍ ትል ይይዛል"
    },
    "you can't judge a book by its cover": {
      "es": "no juzgues un libro por su portada",
      "fr": "l'habit ne fait pas le moine",
      "de": "man soll ein Buch nicht nach seinem Einband beurteilen",
      "am": "መጽሐፍን በሽፋኑ መፍረድ አይችልም"
    }
  };

  return translations[phrase.toLowerCase()] || {};
}

// Pre-defined definitions for common English idioms
function getPhraseDefinition(phrase: string): string {
  const definitions: Record<string, string> = {
    "break the ice": "To initiate conversation or social interaction in a comfortable way",
    "hit the books": "To study hard or intensively",
    "piece of cake": "Something that is very easy to do",
    "spill the beans": "To reveal a secret or disclose confidential information",
    "cost an arm and a leg": "To be very expensive",
    "bite the bullet": "To face a difficult situation with courage",
    "break a leg": "Good luck! (used especially in theater)",
    "call it a day": "To stop working or end an activity",
    "better late than never": "It's better to do something late than not at all",
    "when pigs fly": "Never; something that will never happen",
    "don't cry over spilled milk": "Don't worry about past mistakes that cannot be changed",
    "cut to the chase": "Get to the point; skip the unnecessary details",
    "get the ball rolling": "Start something; begin an activity or process",
    "hang in there": "Persevere; don't give up during difficult times",
    "it's raining cats and dogs": "It's raining very heavily",
    "kill two birds with one stone": "Accomplish two things with one action",
    "let the cat out of the bag": "Reveal a secret accidentally",
    "once in a blue moon": "Very rarely; almost never",
    "the ball is in your court": "It's your turn to take action or make a decision",
    "under the weather": "Feeling sick or unwell",
    "a blessing in disguise": "Something that seems bad but turns out to be good",
    "every cloud has a silver lining": "There's something positive in every negative situation",
    "go the extra mile": "Make an additional effort; do more than expected",
    "it takes two to tango": "Both parties are responsible for a situation",
    "keep your chin up": "Stay positive; don't get discouraged",
    "make a long story short": "To summarize; to get to the point quickly",
    "no pain no gain": "You must work hard or suffer to achieve something worthwhile",
    "practice makes perfect": "Doing something repeatedly will help you become better at it",
    "the early bird catches the worm": "People who wake up early or act quickly will be successful",
    "you can't judge a book by its cover": "Don't judge someone or something based on appearance alone"
  };

  return definitions[phrase.toLowerCase()] || `An English idiom: "${phrase}"`;
}

// Pre-defined example sentences for common English idioms
function getPhraseExamples(phrase: string): string[] {
  const examples: Record<string, string[]> = {
    "break the ice": [
      "John told a funny joke to break the ice at the meeting.",
      "She brought cookies to break the ice with her new neighbors."
    ],
    "hit the books": [
      "I need to hit the books tonight for tomorrow's exam.",
      "After failing the test, Maria decided to hit the books harder."
    ],
    "piece of cake": [
      "The math homework was a piece of cake for Sarah.",
      "Don't worry about the interview, it'll be a piece of cake!"
    ],
    "spill the beans": [
      "Tom accidentally spilled the beans about the surprise party.",
      "Please don't spill the beans about our secret project."
    ],
    "cost an arm and a leg": [
      "That new car costs an arm and a leg!",
      "The designer dress cost an arm and a leg, but it was worth it."
    ],
    "bite the bullet": [
      "I hate going to the dentist, but I'll have to bite the bullet.",
      "She bit the bullet and asked her boss for a raise."
    ],
    "break a leg": [
      "Break a leg in your performance tonight!",
      "The director told all the actors to break a leg before the show."
    ],
    "call it a day": [
      "We've been working for 10 hours, let's call it a day.",
      "After finishing the project, they decided to call it a day."
    ],
    "better late than never": [
      "He arrived an hour late, but better late than never.",
      "I finally learned to drive at 30 - better late than never!"
    ],
    "when pigs fly": [
      "He'll clean his room when pigs fly.",
      "She said she'd apologize when pigs fly."
    ],
    "don't cry over spilled milk": [
      "You failed the test, but don't cry over spilled milk - study harder next time.",
      "The project didn't work out, but don't cry over spilled milk."
    ],
    "cut to the chase": [
      "Let's cut to the chase - what do you really want?",
      "I don't have much time, so let's cut to the chase."
    ],
    "get the ball rolling": [
      "Let's get the ball rolling on this project.",
      "To get the ball rolling, I'll make the first presentation."
    ],
    "hang in there": [
      "I know work is tough right now, but hang in there.",
      "Hang in there - things will get better soon."
    ],
    "it's raining cats and dogs": [
      "We can't go outside - it's raining cats and dogs!",
      "The picnic was cancelled because it was raining cats and dogs."
    ],
    "kill two birds with one stone": [
      "I'll visit the bank and post office together to kill two birds with one stone.",
      "By studying abroad, she killed two birds with one stone - learning the language and getting a degree."
    ],
    "let the cat out of the bag": [
      "Don't let the cat out of the bag about the surprise party!",
      "He accidentally let the cat out of the bag about their engagement."
    ],
    "once in a blue moon": [
      "I only eat fast food once in a blue moon.",
      "She visits her hometown once in a blue moon."
    ],
    "the ball is in your court": [
      "I've made my offer, now the ball is in your court.",
      "We've given you all the information - the ball is in your court."
    ],
    "under the weather": [
      "I'm feeling a bit under the weather today.",
      "She stayed home because she was under the weather."
    ],
    "a blessing in disguise": [
      "Losing that job was a blessing in disguise - I found a much better one.",
      "The rain ruining our picnic was a blessing in disguise - we had a great time indoors."
    ],
    "every cloud has a silver lining": [
      "Don't worry about the setback - every cloud has a silver lining.",
      "Even though he failed the exam, every cloud has a silver lining - now he knows what to study."
    ],
    "go the extra mile": [
      "She always goes the extra mile to help her customers.",
      "If you want to succeed, you need to go the extra mile."
    ],
    "it takes two to tango": [
      "Don't blame just him for the argument - it takes two to tango.",
      "Both companies are responsible for the failed merger - it takes two to tango."
    ],
    "keep your chin up": [
      "I know you're disappointed, but keep your chin up.",
      "Keep your chin up - tomorrow is a new day."
    ],
    "make a long story short": [
      "To make a long story short, we missed our flight.",
      "Make a long story short - did you get the job or not?"
    ],
    "no pain no gain": [
      "Exercise is hard, but no pain no gain.",
      "Learning a new language is difficult, but no pain no gain."
    ],
    "practice makes perfect": [
      "Keep playing piano every day - practice makes perfect.",
      "Don't worry about making mistakes - practice makes perfect."
    ],
    "the early bird catches the worm": [
      "I always arrive at work early - the early bird catches the worm.",
      "She got the best deals at the sale because the early bird catches the worm."
    ],
    "you can't judge a book by its cover": [
      "He looks tough, but he's actually very kind - you can't judge a book by its cover.",
      "The restaurant doesn't look fancy, but the food is amazing - you can't judge a book by its cover."
    ]
  };

  return examples[phrase.toLowerCase()] || [`Here's an example of "${phrase}" in context.`];
}

// Function to translate example sentences with context
async function translateExampleSentence(englishSentence: string, phrase: string, targetLanguage: string): Promise<string> {
  // Get the phrase translation
  const phraseTranslations = getPhraseTranslations(phrase);
  const translatedPhrase = phraseTranslations[targetLanguage] || phrase;

  // Pre-defined full sentence translations for better accuracy
  const sentenceTranslations: Record<string, Record<string, string>> = {
    // get the ball rolling
    "Let's get the ball rolling on this project.": {
      "es": "Pongamos en marcha este proyecto.",
      "am": "ይህንን ፕሮጀክት እንጀምር።",
      "fr": "Lançons ce projet.",
      "de": "Lassen Sie uns dieses Projekt in Gang bringen."
    },
    "To get the ball rolling, I'll make the first presentation.": {
      "es": "Para empezar, haré la primera presentación.",
      "am": "ለመጀመር፣ የመጀመሪያውን አቀራረብ አደርጋለሁ።",
      "fr": "Pour commencer, je ferai la première présentation.",
      "de": "Um anzufangen, werde ich die erste Präsentation machen."
    },

    // break the ice
    "John told a funny joke to break the ice at the meeting.": {
      "es": "John contó un chiste divertido para romper el hielo en la reunión.",
      "am": "ጆን በስብሰባው ላይ በረዶውን ለመስበር አስቂኝ ቀልድ ተናገረ።",
      "fr": "John a raconté une blague amusante pour briser la glace lors de la réunion.",
      "de": "John erzählte einen lustigen Witz, um das Eis bei der Besprechung zu brechen."
    },
    "She brought cookies to break the ice with her new neighbors.": {
      "es": "Trajo galletas para romper el hielo con sus nuevos vecinos.",
      "am": "ከአዳዲስ ጎረቤቶቿ ጋር በረዶውን ለመስበር ኩኪዎችን አመጣች።",
      "fr": "Elle a apporté des biscuits pour briser la glace avec ses nouveaux voisins.",
      "de": "Sie brachte Kekse mit, um das Eis mit ihren neuen Nachbarn zu brechen."
    },

    // hit the books
    "I need to hit the books tonight for tomorrow's exam.": {
      "es": "Necesito estudiar mucho esta noche para el examen de mañana.",
      "am": "ለነገ ፈተና ዛሬ ማታ መጽሐፍትን መምታት አለብኝ።",
      "fr": "Je dois étudier dur ce soir pour l'examen de demain.",
      "de": "Ich muss heute Abend für die morgige Prüfung büffeln."
    },
    "After failing the test, Maria decided to hit the books harder.": {
      "es": "Después de reprobar el examen, María decidió estudiar más duro.",
      "am": "ፈተናውን ካልፈለገች በኋላ ማሪያ መጽሐፍቶቹን በጠንካራ መምታት ወሰነች።",
      "fr": "Après avoir échoué au test, Maria a décidé d'étudier plus dur.",
      "de": "Nachdem sie die Prüfung nicht bestanden hatte, beschloss Maria, härter zu lernen."
    },

    // piece of cake
    "The math homework was a piece of cake for Sarah.": {
      "es": "La tarea de matemáticas fue pan comido para Sarah.",
      "am": "የሂሳብ ስራው ለሳራ የኬክ ቁራጭ ነበር።",
      "fr": "Les devoirs de maths étaient du gâteau pour Sarah.",
      "de": "Die Mathe-Hausaufgaben waren ein Kinderspiel für Sarah."
    },
    "Don't worry about the interview, it'll be a piece of cake!": {
      "es": "No te preocupes por la entrevista, ¡será pan comido!",
      "am": "ስለ ቃለ መጠይቁ አትጨነቅ፣ የኬክ ቁራጭ ይሆናል!",
      "fr": "Ne t'inquiète pas pour l'entretien, ce sera du gâteau !",
      "de": "Mach dir keine Sorgen wegen des Interviews, es wird ein Kinderspiel!"
    },

    // the ball is in your court
    "I've made my offer, now the ball is in your court.": {
      "es": "He hecho mi oferta, ahora la pelota está en tu cancha.",
      "am": "ቀረቡን አቅርቤያለሁ፣ አሁን ኳሱ በአንተ ሜዳ ላይ ነው።",
      "fr": "J'ai fait mon offre, maintenant la balle est dans ton camp.",
      "de": "Ich habe mein Angebot gemacht, jetzt liegt der Ball bei dir."
    },
    "We've given you all the information - the ball is in your court.": {
      "es": "Te hemos dado toda la información - la pelota está en tu cancha.",
      "am": "ሁሉንም መረጃ ሰጥተናችኋል - ኳሱ በአንተ ሜዳ ላይ ነው።",
      "fr": "Nous vous avons donné toutes les informations - la balle est dans votre camp.",
      "de": "Wir haben Ihnen alle Informationen gegeben - der Ball liegt bei Ihnen."
    },

    // hang in there
    "I know work is tough right now, but hang in there.": {
      "es": "Sé que el trabajo está difícil ahora, pero aguanta.",
      "am": "ስራው አሁን ከባድ እንደሆነ አውቃለሁ፣ ግን እዚያ ተንጠልጥል።",
      "fr": "Je sais que le travail est dur en ce moment, mais tiens bon.",
      "de": "Ich weiß, die Arbeit ist gerade schwer, aber halte durch."
    },
    "Hang in there - things will get better soon.": {
      "es": "Aguanta - las cosas mejorarán pronto.",
      "am": "እዚያ ተንጠልጥል - ነገሮች በቅርቡ ይሻላሉ።",
      "fr": "Tiens bon - les choses vont bientôt s'améliorer.",
      "de": "Halte durch - die Dinge werden bald besser."
    },

    // cut to the chase
    "Let's cut to the chase - what do you really want?": {
      "es": "Vamos al grano - ¿qué quieres realmente?",
      "am": "ወደ ዋናው ጉዳይ እንሂድ - በእውነት ምን ትፈልጋለህ?",
      "fr": "Allons droit au but - que veux-tu vraiment ?",
      "de": "Kommen wir zur Sache - was willst du wirklich?"
    },
    "I don't have much time, so let's cut to the chase.": {
      "es": "No tengo mucho tiempo, así que vamos al grano.",
      "am": "ብዙ ጊዜ የለኝም፣ ስለዚህ ወደ ዋናው ጉዳይ እንሂድ።",
      "fr": "Je n'ai pas beaucoup de temps, alors allons droit au but.",
      "de": "Ich habe nicht viel Zeit, also kommen wir zur Sache."
    },

    // go the extra mile
    "She always goes the extra mile to help her customers.": {
      "es": "Ella siempre hace un esfuerzo adicional para ayudar a sus clientes.",
      "am": "እሷ ሁልጊዜ ደንበኞቿን ለመርዳት ተጨማሪ ማይል ትሄዳለች።",
      "fr": "Elle fait toujours un effort supplémentaire pour aider ses clients.",
      "de": "Sie geht immer eine Extrameile, um ihren Kunden zu helfen."
    },
    "If you want to succeed, you need to go the extra mile.": {
      "es": "Si quieres tener éxito, necesitas hacer un esfuerzo adicional.",
      "am": "ለማሳካት ከፈለግክ ተጨማሪ ማይል መሄድ አለብህ።",
      "fr": "Si tu veux réussir, tu dois faire un effort supplémentaire.",
      "de": "Wenn du erfolgreich sein willst, musst du eine Extrameile gehen."
    }
  };

  // Check if we have a pre-defined translation for this exact sentence
  if (sentenceTranslations[englishSentence] && sentenceTranslations[englishSentence][targetLanguage]) {
    return sentenceTranslations[englishSentence][targetLanguage];
  }

  // Try Google Translate for full sentence translation
  try {
    console.log(`Attempting Google Translate for: "${englishSentence}"`);
    const googleTranslation = await translateText(englishSentence, targetLanguage);

    // If Google Translate returned something different from the original, use it
    if (googleTranslation !== englishSentence) {
      console.log(`Google Translate success: "${googleTranslation}"`);
      return googleTranslation;
    }
  } catch (error) {
    console.error('Google Translate failed, using fallback:', error);
  }

  // Enhanced fallback translation with comprehensive patterns
  const enhancedTranslations: Record<string, Record<string, string>> = {
    "am": {
      // Complete sentence patterns for common structures
      "Don't blame just him for the argument": "ለክርክሩ እሱን ብቻ አትወቅስ",
      "Both companies are responsible for the failed merger": "ሁለቱም ኩባንያዎች ለያልተሳካው ውህደት ተጠያቂ ናቸው",
      "We can't go outside": "ወደ ውጭ መውጣት አንችልም",
      "The picnic was cancelled because": "ፒክኒኩ ተሰርዟል ምክንያቱም",
      "Break a leg in your performance tonight": "ዛሬ ማታ በአፈጻጸምህ ላይ እግርህን ስበር",
      "The director told all the actors to": "ዳይሬክተሩ ለሁሉም ተዋናዮች ተናግሯል",
      "You failed the test, but": "ፈተናውን ወድቀሃል፣ ግን",
      "study harder next time": "በሚቀጥለው ጊዜ በጠንካራ ተማር",
      "The project didn't work out": "ፕሮጀክቱ አልሰራም",
      "That new car costs": "ያ አዲስ መኪና ዋጋው",
      "The designer dress cost": "የዲዛይነር ቀሚሱ ዋጋ",
      "but it was worth it": "ግን ዋጋው ነበረው",

      // Word-level patterns
      "Don't blame": "አትወቅስ",
      "just him": "እሱን ብቻ",
      "for the": "ለ",
      "argument": "ክርክር",
      "Both companies": "ሁለቱም ኩባንያዎች",
      "are responsible": "ተጠያቂ ናቸው",
      "failed merger": "ያልተሳካ ውህደት",
      "We can't": "አንችልም",
      "go outside": "ወደ ውጭ መሄድ",
      "Break a leg": "እግርህን ስበር",
      "performance": "አፈጻጸም",
      "tonight": "ዛሬ ማታ",
      "director": "ዳይሬክተር",
      "actors": "ተዋናዮች",
      "You failed": "ወድቀሃል",
      "the test": "ፈተናውን",
      "study harder": "በጠንካራ ተማር",
      "next time": "በሚቀጥለው ጊዜ",
      "project": "ፕሮጀክት",
      "didn't work out": "አልሰራም",
      "new car": "አዲስ መኪና",
      "costs": "ዋጋው",
      "designer dress": "የዲዛይነር ቀሚስ",
      "worth it": "ዋጋው ነበረው",
      "Let's": "እንሂድ",
      "I'll": "እኔ እሰራለሁ",
      "To": "ለ",
      "I need to": "እኔ መሰራት አለብኝ",
      "Don't worry about": "አትጨነቅ ስለ",
      "it'll be": "ይሆናል",
      "I've made": "አድርጌያለሁ",
      "now": "አሁን",
      "We've given you": "ሰጥተናችኋል"
    },
    "es": {
      // Complete sentence patterns for common structures
      "Don't blame just him for the argument": "No culpes solo a él por la discusión",
      "Both companies are responsible for the failed merger": "Ambas empresas son responsables de la fusión fallida",
      "We can't go outside": "No podemos salir",
      "The picnic was cancelled because": "El picnic fue cancelado porque",
      "Break a leg in your performance tonight": "Que tengas suerte en tu actuación esta noche",
      "The director told all the actors to": "El director les dijo a todos los actores que",
      "You failed the test, but": "Reprobaste el examen, pero",
      "study harder next time": "estudia más duro la próxima vez",
      "The project didn't work out": "El proyecto no funcionó",
      "That new car costs": "Ese auto nuevo cuesta",
      "The designer dress cost": "El vestido de diseñador costó",
      "but it was worth it": "pero valió la pena",

      // Word-level patterns
      "Don't blame": "No culpes",
      "just him": "solo a él",
      "for the": "por la",
      "argument": "discusión",
      "Both companies": "Ambas empresas",
      "are responsible": "son responsables",
      "failed merger": "fusión fallida",
      "We can't": "No podemos",
      "go outside": "salir",
      "Break a leg": "Que tengas suerte",
      "performance": "actuación",
      "tonight": "esta noche",
      "director": "director",
      "actors": "actores",
      "You failed": "Reprobaste",
      "the test": "el examen",
      "study harder": "estudia más duro",
      "next time": "la próxima vez",
      "project": "proyecto",
      "didn't work out": "no funcionó",
      "new car": "auto nuevo",
      "costs": "cuesta",
      "designer dress": "vestido de diseñador",
      "worth it": "valió la pena",
      "Let's": "Vamos a",
      "I'll": "Voy a",
      "To": "Para",
      "I need to": "Necesito",
      "Don't worry about": "No te preocupes por",
      "it'll be": "será",
      "I've made": "He hecho",
      "now": "ahora",
      "We've given you": "Te hemos dado"
    }
  };

  // Try enhanced sentence-level translation first
  if (enhancedTranslations[targetLanguage]) {
    // Check for complete sentence matches first
    for (const [englishPattern, translation] of Object.entries(enhancedTranslations[targetLanguage])) {
      if (englishSentence.toLowerCase().includes(englishPattern.toLowerCase())) {
        // If we find a sentence pattern, use it as base and replace the phrase
        let result = englishSentence.replace(new RegExp(englishPattern, 'gi'), translation);
        result = result.replace(new RegExp(phrase, 'gi'), translatedPhrase);
        return result;
      }
    }

    // If no sentence pattern matches, do word-by-word replacement
    let result = englishSentence.replace(new RegExp(phrase, 'gi'), translatedPhrase);

    // Apply word-level patterns
    Object.entries(enhancedTranslations[targetLanguage]).forEach(([english, translated]) => {
      result = result.replace(new RegExp(english, 'gi'), translated);
    });

    return result;
  }

  // Final fallback: just replace the phrase
  return englishSentence.replace(new RegExp(phrase, 'gi'), translatedPhrase);
}

// Function to get similar phrases/synonyms
function getSimilarPhrases(phrase: string): string[] {
  const similarPhrases: Record<string, string[]> = {
    "break the ice": ["start a conversation", "make introductions", "ease tension"],
    "hit the books": ["study hard", "crack the books", "burn the midnight oil"],
    "piece of cake": ["easy as pie", "a walk in the park", "child's play"],
    "spill the beans": ["let the cat out of the bag", "reveal the secret", "tell all"],
    "cost an arm and a leg": ["very expensive", "break the bank", "cost a fortune"],
    "bite the bullet": ["face the music", "take the plunge", "grin and bear it"],
    "break a leg": ["good luck", "knock 'em dead", "best wishes"],
    "call it a day": ["wrap it up", "finish up", "call it quits"],
    "cut to the chase": ["get to the point", "cut to the quick", "bottom line"],
    "get the ball rolling": ["start the ball rolling", "kick things off", "get started"],
    "hang in there": ["stick with it", "don't give up", "persevere"],
    "it's raining cats and dogs": ["pouring rain", "heavy downpour", "bucketing down"],
    "kill two birds with one stone": ["hit two targets", "double up", "multitask"],
    "let the cat out of the bag": ["spill the beans", "reveal the secret", "blow the whistle"],
    "once in a blue moon": ["rarely", "seldom", "hardly ever"],
    "the ball is in your court": ["your turn", "your move", "up to you"],
    "under the weather": ["feeling sick", "not well", "out of sorts"],
    "better late than never": ["better than not at all", "at least you made it", "finally"],
    "when pigs fly": ["never", "not in a million years", "over my dead body"],
    "don't cry over spilled milk": ["what's done is done", "let bygones be bygones", "move on"],
    "a blessing in disguise": ["silver lining", "hidden benefit", "good fortune"],
    "every cloud has a silver lining": ["look on the bright side", "there's hope", "positive outlook"],
    "go the extra mile": ["above and beyond", "give 110%", "exceed expectations"],
    "it takes two to tango": ["both sides responsible", "mutual responsibility", "shared blame"],
    "keep your chin up": ["stay positive", "don't lose hope", "remain optimistic"],
    "make a long story short": ["in summary", "to cut to the chase", "bottom line"],
    "no pain no gain": ["work hard for success", "sacrifice for achievement", "earn your rewards"],
    "practice makes perfect": ["repetition improves skill", "keep trying", "perfect through practice"],
    "the early bird catches the worm": ["first come first served", "early advantage", "punctuality pays"],
    "you can't judge a book by its cover": ["don't judge by appearance", "looks deceive", "inner beauty matters"]
  };

  return similarPhrases[phrase.toLowerCase()] || ["Similar expressions", "Related phrases", "Comparable idioms"];
}

// Sample phrases for demo mode (since database is disabled)
const samplePhrases = [
  {
    id: '1',
    englishPhrase: 'Break the ice',
    phraseType: 'idiom',
    translations: {
      am: 'በረዶውን መስበር',
      es: 'Romper el hielo',
      zh: '打破僵局',
      fr: 'Briser la glace',
      de: 'Das Eis brechen',
      ja: '緊張をほぐす',
      ko: '어색함을 깨다',
      ru: 'Растопить лед',
      ar: 'كسر الجليد',
      vi: 'Phá vỡ sự ngượng ngùng'
    },
    exampleSentences: [
      {
        english: 'He told a joke to break the ice at the meeting.',
        translated: {
          am: 'በስብሰባው ላይ በረዶውን ለመስበር ቀልድ ተናገረ።',
          es: 'Contó un chiste para romper el hielo en la reunión.',
          zh: '他讲了个笑话来打破会议上的僵局。',
          fr: 'Il a raconté une blague pour briser la glace lors de la réunion.',
          de: 'Er erzählte einen Witz, um das Eis bei der Besprechung zu brechen.',
          ja: '彼は会議で緊張をほぐすためにジョークを言った。',
          ko: '그는 회의에서 어색함을 깨기 위해 농담을 했다.',
          ru: 'Он рассказал шутку, чтобы растопить лед на встрече.',
          ar: 'حكى نكتة لكسر الجليد في الاجتماع.',
          vi: 'Anh ấy kể một câu chuyện cười để phá vỡ sự ngượng ngùng trong cuộc họp.'
        }
      }
    ],
    synonyms: ['Start a conversation', 'Make people feel comfortable', 'Ease tension']
  },
  {
    id: '2',
    englishPhrase: 'Hit the books',
    phraseType: 'idiom',
    translations: {
      am: 'መጽሐፍትን መምታት',
      es: 'Ponerse a estudiar',
      zh: '开始学习',
      fr: 'Se mettre aux études',
      de: 'Bücher wälzen',
      ja: '勉強する',
      ko: '공부하다',
      ru: 'Засесть за книги',
      ar: 'البدء في الدراسة',
      vi: 'Bắt đầu học bài'
    },
    exampleSentences: [
      {
        english: 'I need to hit the books if I want to pass the exam.',
        translated: {
          am: 'ፈተናውን ማለፍ ከፈለግሁ መጽሐፍትን መምታት አለብኝ።',
          es: 'Necesito ponerme a estudiar si quiero aprobar el examen.',
          zh: '如果我想通过考试，我需要开始学习。',
          fr: 'Je dois me mettre aux études si je veux réussir l\'examen.',
          de: 'Ich muss die Bücher wälzen, wenn ich die Prüfung bestehen will.',
          ja: '試験に合格したいなら勉強しなければならない。',
          ko: '시험에 합격하려면 공부해야 한다.',
          ru: 'Мне нужно засесть за книги, если я хочу сдать экзамен.',
          ar: 'أحتاج للبدء في الدراسة إذا كنت أريد النجاح في الامتحان.',
          vi: 'Tôi cần bắt đầu học bài nếu muốn đậu kỳ thi.'
        }
      }
    ],
    synonyms: ['Study hard', 'Focus on learning', 'Prepare for exams']
  },
  {
    id: '3',
    englishPhrase: 'Piece of cake',
    phraseType: 'idiom',
    translations: {
      am: 'የዳቦ ቁራሽ',
      es: 'Pan comido',
      zh: '小菜一碟',
      fr: 'Du gâteau',
      de: 'Ein Kinderspiel',
      ja: '朝飯前',
      ko: '식은 죽 먹기',
      ru: 'Проще простого',
      ar: 'أمر سهل جداً',
      vi: 'Dễ như ăn bánh'
    },
    exampleSentences: [
      {
        english: 'Don\'t worry about the test, it\'s a piece of cake!',
        translated: {
          am: 'ስለ ፈተናው አትጨነቅ፣ የዳቦ ቁራሽ ነው!',
          es: 'No te preocupes por el examen, ¡es pan comido!',
          zh: '别担心考试，这是小菜一碟！',
          fr: 'Ne t\'inquiète pas pour le test, c\'est du gâteau !',
          de: 'Mach dir keine Sorgen wegen der Prüfung, das ist ein Kinderspiel!',
          ja: 'テストのことは心配しないで、朝飯前だよ！',
          ko: '시험 걱정하지 마, 식은 죽 먹기야!',
          ru: 'Не волнуйся насчет теста, это проще простого!',
          ar: 'لا تقلق بشأن الاختبار، إنه أمر سهل جداً!',
          vi: 'Đừng lo lắng về bài kiểm tra, nó dễ như ăn bánh!'
        }
      }
    ],
    synonyms: ['Very easy', 'Simple task', 'No problem']
  },
  {
    id: '4',
    englishPhrase: 'Spill the beans',
    phraseType: 'idiom',
    translations: {
      am: 'ሚስጢሩን መናገር',
      es: 'Soltar la sopa',
      zh: '泄露秘密',
      fr: 'Vendre la mèche',
      de: 'Die Katze aus dem Sack lassen',
      ja: '秘密をばらす',
      ko: '비밀을 털어놓다',
      ru: 'Выдать секрет',
      ar: 'كشف السر',
      vi: 'Tiết lộ bí mật'
    },
    exampleSentences: [
      {
        english: 'Come on, spill the beans! What happened at the party?',
        translated: {
          am: 'ና፣ ሚስጢሩን ንገረኝ! በድግሱ ላይ ምን ሆነ?',
          es: '¡Vamos, suelta la sopa! ¿Qué pasó en la fiesta?',
          zh: '快说吧，泄露秘密！聚会上发生了什么？',
          fr: 'Allez, vends la mèche ! Que s\'est-il passé à la fête ?',
          de: 'Komm schon, lass die Katze aus dem Sack! Was ist auf der Party passiert?',
          ja: 'さあ、秘密をばらして！パーティーで何があったの？',
          ko: '자, 비밀을 털어놔! 파티에서 무슨 일이 있었어?',
          ru: 'Давай, выдай секрет! Что случилось на вечеринке?',
          ar: 'هيا، اكشف السر! ماذا حدث في الحفلة؟',
          vi: 'Nào, tiết lộ bí mật đi! Chuyện gì đã xảy ra ở bữa tiệc?'
        }
      }
    ],
    synonyms: ['Reveal a secret', 'Tell the truth', 'Confess']
  },
  {
    id: '5',
    englishPhrase: 'Cost an arm and a leg',
    phraseType: 'idiom',
    translations: {
      am: 'በጣም ውድ መሆን',
      es: 'Costar un ojo de la cara',
      zh: '非常昂贵',
      fr: 'Coûter les yeux de la tête',
      de: 'Ein Vermögen kosten',
      ja: 'とても高い',
      ko: '매우 비싸다',
      ru: 'Стоить целое состояние',
      ar: 'يكلف ثروة',
      vi: 'Rất đắt đỏ'
    },
    exampleSentences: [
      {
        english: 'That new car costs an arm and a leg!',
        translated: {
          am: 'ያ አዲስ መኪና በጣም ውድ ነው!',
          es: '¡Ese coche nuevo cuesta un ojo de la cara!',
          zh: '那辆新车非常昂贵！',
          fr: 'Cette nouvelle voiture coûte les yeux de la tête !',
          de: 'Das neue Auto kostet ein Vermögen!',
          ja: 'その新しい車はとても高い！',
          ko: '그 새 차는 매우 비싸다!',
          ru: 'Эта новая машина стоит целое состояние!',
          ar: 'تلك السيارة الجديدة تكلف ثروة!',
          vi: 'Chiếc xe mới đó rất đắt đỏ!'
        }
      }
    ],
    synonyms: ['Very expensive', 'Overpriced', 'Costly']
  }
];

export async function GET(request: Request) {
  try {
    const session = await getServerSession(authOptions);

    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    const { searchParams } = new URL(request.url);
    const preferredLanguage = session.user.preferredLanguage || 'es';
    const userId = session.user.id!;
    console.log(`User ${userId} preferred language: ${preferredLanguage}`);

    // Get list of known phrases for this user to avoid repetition
    let knownPhraseIds: string[] = [];
    try {
      const prisma = new PrismaClient();
      const knownPhrases = await prisma.userPhrase.findMany({
        where: {
          userId: userId,
          status: 'known'
        },
        select: {
          phraseId: true
        }
      });
      knownPhraseIds = knownPhrases.map(p => p.phraseId);
      console.log(`User has ${knownPhraseIds.length} known phrases`);
      await prisma.$disconnect();
    } catch (error) {
      console.error('Error fetching known phrases:', error);
    }

    // 70% chance to get a dynamic phrase from Wordnik, 30% chance to get a static phrase
    const useDynamicPhrase = Math.random() < 0.7;

    if (useDynamicPhrase) {
      try {
        console.log('Attempting to fetch dynamic phrase from Wordnik...');

        // Fetch a random phrase from Wordnik
        const wordnikPhrase = await getRandomPhrase();

        if (wordnikPhrase && wordnikPhrase.phrase) {
          console.log(`Got phrase from Wordnik: "${wordnikPhrase.phrase}"`);

          // Create a definition from Wordnik data or use pre-defined ones
          const definition = wordnikPhrase.definitions.length > 0
            ? wordnikPhrase.definitions[0].text
            : getPhraseDefinition(wordnikPhrase.phrase);

          // Create example sentences from Wordnik data
          const examples = wordnikPhrase.examples.slice(0, 2);

          // If no examples from Wordnik, create meaningful ones
          if (examples.length === 0) {
            const exampleSentences = getPhraseExamples(wordnikPhrase.phrase);
            examples.push(...exampleSentences.map(text => ({ text, title: 'Generated' })));
          }

          // Use pre-defined translations for common phrases (since Google Translate has issues)
          const phraseTranslations = getPhraseTranslations(wordnikPhrase.phrase);
          const translatedPhrase = phraseTranslations[preferredLanguage] || wordnikPhrase.phrase;
          console.log(`Phrase: "${wordnikPhrase.phrase}" -> "${translatedPhrase}" (${preferredLanguage})`);

          const translatedExamples = await Promise.all(examples.map(async (example) => ({
            english: example.text,
            translated: {
              [preferredLanguage]: await translateExampleSentence(example.text, wordnikPhrase.phrase, preferredLanguage)
            }
          })));

          // Create consistent ID for the phrase
          const phraseId = `wordnik_${wordnikPhrase.phrase.replace(/\s+/g, '_').toLowerCase().replace(/[^a-z0-9_]/g, '')}`;

          // Skip if user already knows this phrase
          if (knownPhraseIds.includes(phraseId)) {
            console.log(`Skipping known phrase: ${wordnikPhrase.phrase}`);
            // Try to get another phrase (recursive call with limit to prevent infinite loop)
            const retryCount = parseInt(searchParams.get('retryCount') || '0');
            if (retryCount < 3) {
              const retryUrl = new URL(request.url);
              retryUrl.searchParams.set('retryCount', (retryCount + 1).toString());
              return GET(new Request(retryUrl.toString()));
            }
          }

          // Format the response to match our Phrase interface
          const formattedPhrase = {
            id: phraseId,
            englishPhrase: wordnikPhrase.phrase,
            phraseType: 'idiom',
            translations: {
              [preferredLanguage]: translatedPhrase
            },
            exampleSentences: translatedExamples,
            synonyms: getSimilarPhrases(wordnikPhrase.phrase),
            source: 'Wordnik + Google Translate'
          };

          console.log('Successfully created dynamic phrase');
          return NextResponse.json(formattedPhrase);
        }
      } catch (error) {
        console.log('Dynamic phrase fetch failed, falling back to static phrases:', error);
      }
    }

    // Fallback to static phrases (or if dynamic fetch failed)
    console.log('Using static phrase');

    // Filter out known phrases from static phrases
    const availablePhrases = samplePhrases.filter(phrase => !knownPhraseIds.includes(phrase.id));

    if (availablePhrases.length === 0) {
      // If user knows all phrases, reset and show a random one anyway
      console.log('User knows all available phrases, showing random phrase');
      const randomIndex = Math.floor(Math.random() * samplePhrases.length);
      return NextResponse.json(samplePhrases[randomIndex]);
    }

    const randomIndex = Math.floor(Math.random() * availablePhrases.length);
    const randomPhrase = availablePhrases[randomIndex];

    return NextResponse.json(randomPhrase);
  } catch (error) {
    console.error("Error fetching random phrase:", error);
    return NextResponse.json({ error: "Failed to fetch phrase" }, { status: 500 });
  }
}