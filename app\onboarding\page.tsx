'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';

const languages = [
  { code: 'am', name: 'Amharic' },
  { code: 'es', name: 'Spanish' },
  { code: 'zh', name: 'Chinese' },
  { code: 'vi', name: 'Vietnamese' },
  { code: 'fr', name: 'French' },
  { code: 'de', name: 'German' },
  { code: 'ja', name: 'Japanese' },
  { code: 'ko', name: 'Korean' },
  { code: 'ru', name: 'Russian' },
  { code: 'ar', name: 'Arabic' },
];

export default function Onboarding() {
  const { data: session, update } = useSession();
  const [selectedLanguage, setSelectedLanguage] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const router = useRouter();

  // Check if user already has a language preference
  useEffect(() => {
    if (session?.user?.preferredLanguage) {
      console.log(`User already has language preference: ${session.user.preferredLanguage}, redirecting to learn page`);
      router.push('/learn');
    }
  }, [session, router]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!selectedLanguage) return;
    
    setIsSubmitting(true);
    
    try {
      const response = await fetch('/api/user/language', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ preferredLanguage: selectedLanguage }),
      });
      
      if (!response.ok) {
        throw new Error('Failed to update language preference');
      }
      
      // Update the session with the new preferred language
      await update({
        ...session,
        user: {
          ...session?.user,
          preferredLanguage: selectedLanguage,
        },
      });
      
      // Redirect to the learning page
      router.push('/learn');
    } catch (error) {
      console.error('Error updating language preference:', error);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div className="flex flex-col items-center justify-center min-h-screen p-4">
      <div className="w-full max-w-lg card p-8 animate-slide-up">
        {/* Header */}
        <div className="text-center mb-8">
          <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-emerald-500 to-blue-600 rounded-2xl shadow-lg mb-4 animate-bounce-gentle">
            <svg className="w-8 h-8 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129" />
            </svg>
          </div>
          <h1 className="text-3xl font-bold text-slate-800 mb-3">Welcome to English Phrase Pro!</h1>
          <p className="text-lg text-slate-600 mb-2">
            You're all set! 🎉
          </p>
          <p className="text-slate-500">
            Choose your preferred language for translations to get started:
          </p>
        </div>
        
        <form onSubmit={handleSubmit} className="space-y-6">
          {/* Language Selection Grid */}
          <div className="grid grid-cols-2 gap-4">
            {languages.map((language, index) => (
              <button
                key={language.code}
                type="button"
                onClick={() => setSelectedLanguage(language.code)}
                className={`group relative p-4 border-2 rounded-xl text-center transition-all duration-200 hover:scale-105 ${
                  selectedLanguage === language.code
                    ? 'bg-gradient-to-br from-blue-50 to-purple-50 border-blue-400 text-blue-700 shadow-glow'
                    : 'border-slate-200 hover:border-slate-300 hover:bg-slate-50 text-slate-700'
                }`}
                style={{
                  animationDelay: `${index * 50}ms`
                }}
              >
                {/* Selection indicator */}
                {selectedLanguage === language.code && (
                  <div className="absolute -top-2 -right-2 w-6 h-6 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center animate-bounce-gentle">
                    <svg className="w-3 h-3 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M5 13l4 4L19 7" />
                    </svg>
                  </div>
                )}

                <div className="font-semibold text-lg mb-1">{language.name}</div>
                <div className="text-xs text-slate-500 uppercase tracking-wide">{language.code}</div>

                {/* Hover effect */}
                <div className="absolute inset-0 rounded-xl bg-gradient-to-br from-blue-400/0 to-purple-400/0 group-hover:from-blue-400/5 group-hover:to-purple-400/5 transition-all duration-200"></div>
              </button>
            ))}
          </div>

          {/* Progress indicator */}
          {selectedLanguage && (
            <div className="text-center animate-fade-in">
              <div className="inline-flex items-center gap-2 text-sm text-emerald-600 bg-emerald-50 px-4 py-2 rounded-full">
                <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 13l4 4L19 7" />
                </svg>
                Language selected! Ready to continue.
              </div>
            </div>
          )}

          {/* Submit Button */}
          <button
            type="submit"
            disabled={!selectedLanguage || isSubmitting}
            className="w-full btn-primary text-lg py-4 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none disabled:shadow-none"
          >
            {isSubmitting ? (
              <div className="flex items-center justify-center gap-3">
                <svg className="animate-spin w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                </svg>
                Setting up your account...
              </div>
            ) : (
              <div className="flex items-center justify-center gap-3">
                Start Learning
                <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 7l5 5m0 0l-5 5m5-5H6" />
                </svg>
              </div>
            )}
          </button>

          {/* Help text */}
          <div className="text-center text-sm text-slate-500">
            Don't worry, you can change this later in your settings.
          </div>
        </form>
      </div>
    </div>
  );
}