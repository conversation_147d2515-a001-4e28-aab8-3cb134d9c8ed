# 🔒 Security Guidelines

## Environment Variables Security

### ⚠️ CRITICAL: Never Commit Real API Keys

**Files that should NEVER be committed to version control:**
- `.env`
- `.env.local`
- `.env.development.local`
- `.env.production.local`
- Any file containing real API keys or credentials

### ✅ Safe Files to Commit

**Files that are safe to commit:**
- `.env.example` - Template with placeholder values
- `.gitignore` - Ensures sensitive files are ignored
- `ENVIRONMENT_SETUP.md` - Setup instructions
- `SECURITY.md` - This security guide

### 🔧 Development Setup

1. **Copy the example file:**
   ```bash
   cp .env.example .env.development.local
   ```

2. **Fill in your real API keys in `.env.development.local`**

3. **Verify `.env.development.local` is in `.gitignore`**

### 🚨 If You Accidentally Committed API Keys

If you accidentally committed real API keys:

1. **Immediately rotate all exposed keys:**
   - Google OAuth: Create new credentials
   - Google Translate API: Generate new key
   - Wordnik API: Generate new key
   - Database: Change passwords

2. **Remove from git history:**
   ```bash
   git filter-branch --force --index-filter \
   'git rm --cached --ignore-unmatch .env.local' \
   --prune-empty --tag-name-filter cat -- --all
   ```

3. **Force push to remove from remote:**
   ```bash
   git push origin --force --all
   ```

### 🔐 API Key Security Best Practices

1. **Use different keys for development and production**
2. **Regularly rotate API keys**
3. **Set up API key restrictions where possible**
4. **Monitor API usage for unusual activity**
5. **Use environment-specific configuration**

### 📋 Environment File Priority

Next.js loads environment files in this order:
1. `.env.development.local` (highest priority for development)
2. `.env.local`
3. `.env.development`
4. `.env` (lowest priority)

### 🛡️ Production Deployment

For production:
1. **Never use development keys in production**
2. **Set environment variables in your hosting platform**
3. **Use secrets management services when possible**
4. **Enable API key restrictions for production domains**

### 📞 Security Contact

If you discover a security vulnerability, please report it responsibly.
