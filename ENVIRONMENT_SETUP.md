# Environment Setup Guide

This guide explains how to set up the environment variables for the English Phrase Pro application.

## 🔧 Required Environment Variables

### 1. Copy the Example File
```bash
cp .env.example .env.local
```

### 2. Fill in Your API Keys

Edit `.env.local` with your actual values:

#### **NextAuth Configuration**
```env
NEXTAUTH_URL=http://localhost:3000
NEXTAUTH_SECRET=your-secret-key-here
```
- Generate a secret key: `openssl rand -base64 32`

#### **Google OAuth Setup**
```env
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
```

**How to get Google OAuth credentials:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable Google+ API
4. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
5. Set authorized redirect URIs: `http://localhost:3000/api/auth/callback/google`

#### **Database Configuration**
```env
DATABASE_URL="postgresql://username:password@localhost:5432/english_phrase_pro"
```

**For Supabase:**
```env
DATABASE_URL="postgresql://postgres:[YOUR-PASSWORD]@db.[YOUR-PROJECT-REF].supabase.co:5432/postgres"
```

#### **Google Translate API**
```env
GOOGLE_TRANSLATE_API_KEY=your-google-translate-api-key-here
```

**How to get Google Translate API key:**
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Enable Cloud Translation API
3. Go to "Credentials" → "Create Credentials" → "API Key"
4. **Important**: Set Application restrictions to "None" for server-side usage

#### **Wordnik API**
```env
WORDNIK_API_KEY=your-wordnik-api-key-here
```

**How to get Wordnik API key:**
1. Go to [Wordnik Developer Portal](https://developer.wordnik.com/)
2. Sign up for a free account
3. Create a new application
4. Copy your API key

## 🔒 Security Notes

- **Never commit `.env` or `.env.local` files to version control**
- Keep your API keys secure and don't share them
- Use different keys for development and production
- Regularly rotate your API keys

## 🚀 Production Deployment

For production deployment (Vercel, Netlify, etc.), add these environment variables in your hosting platform's dashboard:

### Vercel
1. Go to your project dashboard
2. Settings → Environment Variables
3. Add each variable with its value

### Netlify
1. Go to Site settings
2. Environment variables
3. Add each variable

## 📝 Environment Variables Summary

| Variable | Required | Description |
|----------|----------|-------------|
| `NEXTAUTH_URL` | ✅ | Your app's URL |
| `NEXTAUTH_SECRET` | ✅ | Secret for JWT signing |
| `GOOGLE_CLIENT_ID` | ✅ | Google OAuth client ID |
| `GOOGLE_CLIENT_SECRET` | ✅ | Google OAuth client secret |
| `DATABASE_URL` | ✅ | PostgreSQL connection string |
| `GOOGLE_TRANSLATE_API_KEY` | ✅ | Google Translate API key |
| `WORDNIK_API_KEY` | ✅ | Wordnik API key for phrases |

## 🆘 Troubleshooting

### Google Translate API Issues
- Ensure API key has no application restrictions for server-side usage
- Check that Cloud Translation API is enabled
- Verify billing is set up (required for API usage)

### OAuth Issues
- Check redirect URIs match exactly
- Ensure Google+ API is enabled
- Verify client ID and secret are correct

### Database Issues
- Test connection string with a database client
- Ensure database exists and is accessible
- Check firewall settings for remote databases
