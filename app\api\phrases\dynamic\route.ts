import { NextResponse } from "next/server";
import { getServerSession } from "next-auth";
import { authOptions } from "../../auth/[...nextauth]/route";
import { getRandomPhrase, translateText } from "@/lib/wordnik";

export async function GET() {
  try {
    const session = await getServerSession(authOptions);
    
    if (!session?.user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's preferred language
    const preferredLanguage = session.user.preferredLanguage || 'es';
    
    // Fetch a random phrase from Wordnik
    const wordnikPhrase = await getRandomPhrase();
    
    if (!wordnikPhrase) {
      return NextResponse.json({ error: "Failed to fetch phrase from Wordnik" }, { status: 500 });
    }

    // Create a definition from Wordnik data
    const definition = wordnikPhrase.definitions.length > 0 
      ? wordnikPhrase.definitions[0].text 
      : `An English idiom: "${wordnikPhrase.phrase}"`;

    // Create example sentences from Wordnik data
    const examples = wordnikPhrase.examples.slice(0, 2).map(example => ({
      english: example.text,
      source: example.title || 'Wordnik'
    }));

    // If no examples from Wordnik, create a basic one
    if (examples.length === 0) {
      examples.push({
        english: `Here's an example of "${wordnikPhrase.phrase}" in context.`,
        source: 'Generated'
      });
    }

    // Translate the phrase and examples
    const translatedPhrase = await translateText(wordnikPhrase.phrase, preferredLanguage);
    const translatedDefinition = await translateText(definition, preferredLanguage);
    
    const translatedExamples = await Promise.all(
      examples.map(async (example) => ({
        english: example.english,
        translated: {
          [preferredLanguage]: await translateText(example.english, preferredLanguage)
        }
      }))
    );

    // Format the response to match our Phrase interface
    const formattedPhrase = {
      id: `wordnik_${Date.now()}`,
      englishPhrase: wordnikPhrase.phrase,
      phraseType: 'idiom',
      translations: {
        [preferredLanguage]: translatedPhrase
      },
      definition: {
        english: definition,
        translated: translatedDefinition
      },
      exampleSentences: translatedExamples,
      synonyms: [], // Could be enhanced with Wordnik's related words API
      source: 'Wordnik + Google Translate'
    };

    return NextResponse.json(formattedPhrase);
  } catch (error) {
    console.error("Error generating dynamic phrase:", error);
    return NextResponse.json({ error: "Failed to generate phrase" }, { status: 500 });
  }
}
