import NextAuth from "next-auth";
import GoogleProvider from "next-auth/providers/google";
import { PrismaAdapter } from "@auth/prisma-adapter";
import { prisma } from "@/lib/prisma";
import { PrismaClient } from "@prisma/client";

export const authOptions = {
  // Temporarily disable database adapter to test OAuth flow
  // adapter: PrismaAdapter(prisma),
  providers: [
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
    }),
  ],
  callbacks: {
    async session({ session, token }: any) {
      if (session.user && token?.sub) {
        session.user.id = token.sub;
        // Include preferred language from token
        session.user.preferredLanguage = token.preferredLanguage;
      }
      return session;
    },
    async jwt({ token, account, profile, trigger, session }: any) {
      if (account) {
        token.accessToken = account.access_token;

        // When user first signs in, fetch their language preference from database
        try {
          const prismaClient = new PrismaClient();
          let user = await prismaClient.user.findUnique({
            where: { id: token.sub },
            select: { preferredLanguage: true }
          });

          // If user doesn't exist, create them with default language preference
          if (!user) {
            user = await prismaClient.user.create({
              data: {
                id: token.sub,
                name: token.name,
                email: token.email,
                image: token.picture,
                preferredLanguage: 'am' // Default to Amharic
              },
              select: { preferredLanguage: true }
            });
            console.log(`Created new user with default language preference: ${token.sub}`);
          }

          token.preferredLanguage = user.preferredLanguage;
          console.log(`Loaded language preference for user ${token.sub}: ${user.preferredLanguage}`);
          await prismaClient.$disconnect();
        } catch (error) {
          console.error('Error fetching user language preference:', error);
          token.preferredLanguage = 'am'; // Default fallback
        }
      }

      // Handle session updates (like language preference changes)
      if (trigger === "update" && session?.user?.preferredLanguage) {
        token.preferredLanguage = session.user.preferredLanguage;
      }

      return token;
    },
  },
  pages: {
    signIn: "/auth/signin",
  },
};

const handler = NextAuth(authOptions);
export { handler as GET, handler as POST };