/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/learn/page";
exports.ids = ["app/learn/page"];
exports.modules = {

/***/ "@prisma/client":
/*!*********************************!*\
  !*** external "@prisma/client" ***!
  \*********************************/
/***/ ((module) => {

"use strict";
module.exports = require("@prisma/client");

/***/ }),

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "assert":
/*!*************************!*\
  !*** external "assert" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("assert");

/***/ }),

/***/ "buffer":
/*!*************************!*\
  !*** external "buffer" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("buffer");

/***/ }),

/***/ "crypto":
/*!*************************!*\
  !*** external "crypto" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("crypto");

/***/ }),

/***/ "events":
/*!*************************!*\
  !*** external "events" ***!
  \*************************/
/***/ ((module) => {

"use strict";
module.exports = require("events");

/***/ }),

/***/ "http":
/*!***********************!*\
  !*** external "http" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("http");

/***/ }),

/***/ "https":
/*!************************!*\
  !*** external "https" ***!
  \************************/
/***/ ((module) => {

"use strict";
module.exports = require("https");

/***/ }),

/***/ "querystring":
/*!******************************!*\
  !*** external "querystring" ***!
  \******************************/
/***/ ((module) => {

"use strict";
module.exports = require("querystring");

/***/ }),

/***/ "url":
/*!**********************!*\
  !*** external "url" ***!
  \**********************/
/***/ ((module) => {

"use strict";
module.exports = require("url");

/***/ }),

/***/ "util":
/*!***********************!*\
  !*** external "util" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("util");

/***/ }),

/***/ "zlib":
/*!***********************!*\
  !*** external "zlib" ***!
  \***********************/
/***/ ((module) => {

"use strict";
module.exports = require("zlib");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flearn%2Fpage&page=%2Flearn%2Fpage&appPaths=%2Flearn%2Fpage&pagePath=private-next-app-dir%2Flearn%2Fpage.tsx&appDir=D%3A%5CSoftware%20Projects%5CLanguage%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSoftware%20Projects%5CLanguage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flearn%2Fpage&page=%2Flearn%2Fpage&appPaths=%2Flearn%2Fpage&pagePath=private-next-app-dir%2Flearn%2Fpage.tsx&appDir=D%3A%5CSoftware%20Projects%5CLanguage%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSoftware%20Projects%5CLanguage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: [\n        'learn',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/learn/page.tsx */ \"(rsc)/./app/learn/page.tsx\")), \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        \n        \n      }\n      ]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/learn/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/learn/page\",\n        pathname: \"/learn\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flearn%2Fpage&page=%2Flearn%2Fpage&appPaths=%2Flearn%2Fpage&pagePath=private-next-app-dir%2Flearn%2Fpage.tsx&appDir=D%3A%5CSoftware%20Projects%5CLanguage%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSoftware%20Projects%5CLanguage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Ccomponents%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Ccomponents%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/components/AuthProvider.tsx */ \"(ssr)/./app/components/AuthProvider.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTb2Z0d2FyZSUyMFByb2plY3RzJTVDJTVDTGFuZ3VhZ2UlNUMlNUNhcHAlNUMlNUNjb21wb25lbnRzJTVDJTVDQXV0aFByb3ZpZGVyLnRzeCUyMiUyQyUyMmlkcyUyMiUzQSU1QiUyMmRlZmF1bHQlMjIlNUQlN0QmbW9kdWxlcz0lN0IlMjJyZXF1ZXN0JTIyJTNBJTIyRCUzQSU1QyU1Q1NvZnR3YXJlJTIwUHJvamVjdHMlNUMlNUNMYW5ndWFnZSU1QyU1Q25vZGVfbW9kdWxlcyU1QyU1Q25leHQlNUMlNUNmb250JTVDJTVDZ29vZ2xlJTVDJTVDdGFyZ2V0LmNzcyUzRiU3QiU1QyUyMnBhdGglNUMlMjIlM0ElNUMlMjJhcHAlNUMlNUMlNUMlNUNsYXlvdXQudHN4JTVDJTIyJTJDJTVDJTIyaW1wb3J0JTVDJTIyJTNBJTVDJTIySW50ZXIlNUMlMjIlMkMlNUMlMjJhcmd1bWVudHMlNUMlMjIlM0ElNUIlN0IlNUMlMjJzdWJzZXRzJTVDJTIyJTNBJTVCJTVDJTIybGF0aW4lNUMlMjIlNUQlN0QlNUQlMkMlNUMlMjJ2YXJpYWJsZU5hbWUlNUMlMjIlM0ElNUMlMjJpbnRlciU1QyUyMiU3RCUyMiUyQyUyMmlkcyUyMiUzQSU1QiU1RCU3RCZtb2R1bGVzPSU3QiUyMnJlcXVlc3QlMjIlM0ElMjJEJTNBJTVDJTVDU29mdHdhcmUlMjBQcm9qZWN0cyU1QyU1Q0xhbmd1YWdlJTVDJTVDYXBwJTVDJTVDZ2xvYmFscy5jc3MlMjIlMkMlMjJpZHMlMjIlM0ElNUIlNUQlN0Qmc2VydmVyPXRydWUhIiwibWFwcGluZ3MiOiJBQUFBLDhLQUFvSSIsInNvdXJjZXMiOlsid2VicGFjazovL2xhbmd1YWdlLz84OGY0Il0sInNvdXJjZXNDb250ZW50IjpbImltcG9ydCgvKiB3ZWJwYWNrTW9kZTogXCJlYWdlclwiLCB3ZWJwYWNrRXhwb3J0czogW1wiZGVmYXVsdFwiXSAqLyBcIkQ6XFxcXFNvZnR3YXJlIFByb2plY3RzXFxcXExhbmd1YWdlXFxcXGFwcFxcXFxjb21wb25lbnRzXFxcXEF1dGhQcm92aWRlci50c3hcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Ccomponents%5C%5CAuthProvider.tsx%22%2C%22ids%22%3A%5B%22default%22%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Clearn%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!*********************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Clearn%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \*********************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/learn/page.tsx */ \"(ssr)/./app/learn/page.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkQlM0ElNUMlNUNTb2Z0d2FyZSUyMFByb2plY3RzJTVDJTVDTGFuZ3VhZ2UlNUMlNUNhcHAlNUMlNUNsZWFybiU1QyU1Q3BhZ2UudHN4JTIyJTJDJTIyaWRzJTIyJTNBJTVCJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxvSkFBMEYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYW5ndWFnZS8/ZTgyNSJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQoLyogd2VicGFja01vZGU6IFwiZWFnZXJcIiAqLyBcIkQ6XFxcXFNvZnR3YXJlIFByb2plY3RzXFxcXExhbmd1YWdlXFxcXGFwcFxcXFxsZWFyblxcXFxwYWdlLnRzeFwiKTtcbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Capp%5C%5Clearn%5C%5Cpage.tsx%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \****************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22D%3A%5C%5CSoftware%20Projects%5C%5CLanguage%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./app/components/AuthProvider.tsx":
/*!*****************************************!*\
  !*** ./app/components/AuthProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ AuthProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_1__);\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\nfunction AuthProvider({ children, session }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_auth_react__WEBPACK_IMPORTED_MODULE_1__.SessionProvider, {\n        session: session,\n        children: children\n    }, void 0, false, {\n        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\components\\\\AuthProvider.tsx\",\n        lineNumber: 12,\n        columnNumber: 10\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9hcHAvY29tcG9uZW50cy9BdXRoUHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUVrRDtBQUVuQyxTQUFTQyxhQUFhLEVBQ25DQyxRQUFRLEVBQ1JDLE9BQU8sRUFJUjtJQUNDLHFCQUFPLDhEQUFDSCw0REFBZUE7UUFBQ0csU0FBU0E7a0JBQVVEOzs7Ozs7QUFDN0MiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYW5ndWFnZS8uL2FwcC9jb21wb25lbnRzL0F1dGhQcm92aWRlci50c3g/ZDVmMCJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIGNsaWVudCc7XG5cbmltcG9ydCB7IFNlc3Npb25Qcm92aWRlciB9IGZyb20gJ25leHQtYXV0aC9yZWFjdCc7XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIEF1dGhQcm92aWRlcih7IFxuICBjaGlsZHJlbixcbiAgc2Vzc2lvbiBcbn06IHsgXG4gIGNoaWxkcmVuOiBSZWFjdC5SZWFjdE5vZGU7XG4gIHNlc3Npb246IGFueTtcbn0pIHtcbiAgcmV0dXJuIDxTZXNzaW9uUHJvdmlkZXIgc2Vzc2lvbj17c2Vzc2lvbn0+e2NoaWxkcmVufTwvU2Vzc2lvblByb3ZpZGVyPjtcbn0iXSwibmFtZXMiOlsiU2Vzc2lvblByb3ZpZGVyIiwiQXV0aFByb3ZpZGVyIiwiY2hpbGRyZW4iLCJzZXNzaW9uIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./app/components/AuthProvider.tsx\n");

/***/ }),

/***/ "(ssr)/./app/learn/page.tsx":
/*!****************************!*\
  !*** ./app/learn/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Learn)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth/react */ \"(ssr)/./node_modules/next-auth/react/index.js\");\n/* harmony import */ var next_auth_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth_react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/navigation */ \"(ssr)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/link */ \"(ssr)/./node_modules/next/dist/api/link.js\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \n\n\n\n\nfunction Learn() {\n    const { data: session, status } = (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.useSession)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_3__.useRouter)();\n    const [currentPhrase, setCurrentPhrase] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [showTranslation, setShowTranslation] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showExamples, setShowExamples] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showSynonyms, setShowSynonyms] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    // Sample data for demo mode\n    const samplePhrases = [\n        {\n            id: \"1\",\n            englishPhrase: \"Break the ice\",\n            phraseType: \"idiom\",\n            translations: {\n                am: \"በረዶውን መስበር\",\n                es: \"Romper el hielo\",\n                zh: \"打破僵局\",\n                fr: \"Briser la glace\",\n                de: \"Das Eis brechen\"\n            },\n            exampleSentences: [\n                {\n                    english: \"He told a joke to break the ice at the meeting.\",\n                    translated: {\n                        am: \"በስብሰባው ላይ በረዶውን ለመስበር ቀልድ ተናገረ።\",\n                        es: \"Cont\\xf3 un chiste para romper el hielo en la reuni\\xf3n.\",\n                        zh: \"他讲了个笑话来打破会议上的僵局。\",\n                        fr: \"Il a racont\\xe9 une blague pour briser la glace lors de la r\\xe9union.\",\n                        de: \"Er erz\\xe4hlte einen Witz, um das Eis bei der Besprechung zu brechen.\"\n                    }\n                }\n            ],\n            synonyms: [\n                \"Start a conversation\",\n                \"Make people feel comfortable\",\n                \"Ease tension\"\n            ]\n        },\n        {\n            id: \"2\",\n            englishPhrase: \"Hit the books\",\n            phraseType: \"idiom\",\n            translations: {\n                es: \"Ponerse a estudiar\",\n                zh: \"开始学习\",\n                fr: \"Se mettre aux \\xe9tudes\",\n                de: \"Die B\\xfccher w\\xe4lzen\"\n            },\n            exampleSentences: [\n                {\n                    english: \"I need to hit the books for my exam tomorrow.\",\n                    translated: {\n                        es: \"Necesito ponerme a estudiar para mi examen de ma\\xf1ana.\",\n                        zh: \"我需要为明天的考试开始学习。\",\n                        fr: \"Je dois me mettre aux \\xe9tudes pour mon examen de demain.\",\n                        de: \"Ich muss die B\\xfccher w\\xe4lzen f\\xfcr meine Pr\\xfcfung morgen.\"\n                    }\n                }\n            ],\n            synonyms: [\n                \"Study hard\",\n                \"Start studying\",\n                \"Focus on learning\"\n            ]\n        },\n        {\n            id: \"3\",\n            englishPhrase: \"Piece of cake\",\n            phraseType: \"idiom\",\n            translations: {\n                es: \"Pan comido\",\n                zh: \"小菜一碟\",\n                fr: \"Du g\\xe2teau\",\n                de: \"Ein Kinderspiel\"\n            },\n            exampleSentences: [\n                {\n                    english: \"The math test was a piece of cake.\",\n                    translated: {\n                        es: \"El examen de matem\\xe1ticas fue pan comido.\",\n                        zh: \"数学考试是小菜一碟。\",\n                        fr: \"Le test de maths \\xe9tait du g\\xe2teau.\",\n                        de: \"Der Mathe-Test war ein Kinderspiel.\"\n                    }\n                }\n            ],\n            synonyms: [\n                \"Very easy\",\n                \"Simple task\",\n                \"No problem\"\n            ]\n        }\n    ];\n    const [currentPhraseIndex, setCurrentPhraseIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [hasInitialized, setHasInitialized] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [showLanguageModal, setShowLanguageModal] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const [selectedLanguage, setSelectedLanguage] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(session?.user?.preferredLanguage || \"am\");\n    // Prevent auto-refresh when returning to the page\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        const handleVisibilityChange = ()=>{\n        // Do nothing when page becomes visible again\n        // This prevents auto-refresh when returning from other apps/tabs\n        };\n        document.addEventListener(\"visibilitychange\", handleVisibilityChange);\n        return ()=>{\n            document.removeEventListener(\"visibilitychange\", handleVisibilityChange);\n        };\n    }, []);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Only initialize once when the component mounts or authentication status changes\n        if (status === \"loading\" || hasInitialized) return;\n        if (status === \"unauthenticated\") {\n            // In demo mode, show sample phrases\n            setCurrentPhrase(samplePhrases[currentPhraseIndex]);\n            setLoading(false);\n            setHasInitialized(true);\n        } else if (status === \"authenticated\" && !session?.user?.preferredLanguage) {\n            router.push(\"/onboarding\");\n        } else if (status === \"authenticated\") {\n            fetchRandomPhrase();\n            setHasInitialized(true);\n        }\n    }, [\n        status,\n        session,\n        router\n    ]); // Removed currentPhraseIndex from dependencies\n    const fetchRandomPhrase = async ()=>{\n        setLoading(true);\n        try {\n            if (status === \"unauthenticated\") {\n                // Demo mode - use sample data\n                const randomIndex = Math.floor(Math.random() * samplePhrases.length);\n                setCurrentPhraseIndex(randomIndex);\n                setCurrentPhrase(samplePhrases[randomIndex]);\n            } else {\n                // Authenticated mode - fetch from API\n                const response = await fetch(\"/api/phrases/random\");\n                if (!response.ok) throw new Error(\"Failed to fetch phrase\");\n                const data = await response.json();\n                setCurrentPhrase(data);\n            }\n            // Reset states\n            setShowTranslation(false);\n            setShowExamples(false);\n            setShowSynonyms(false);\n        } catch (error) {\n            console.error(\"Error fetching phrase:\", error);\n            // Fallback to demo data if API fails\n            if (samplePhrases.length > 0) {\n                const randomIndex = Math.floor(Math.random() * samplePhrases.length);\n                setCurrentPhraseIndex(randomIndex);\n                setCurrentPhrase(samplePhrases[randomIndex]);\n            }\n        } finally{\n            setLoading(false);\n        }\n    };\n    const handleKnowPhrase = async ()=>{\n        if (!currentPhrase) return;\n        if (status === \"unauthenticated\") {\n            // Demo mode - just fetch a new phrase\n            fetchRandomPhrase();\n            return;\n        }\n        try {\n            await fetch(\"/api/phrases/status\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    phraseId: currentPhrase.id,\n                    status: \"known\",\n                    phraseData: {\n                        englishPhrase: currentPhrase.englishPhrase,\n                        phraseType: currentPhrase.phraseType,\n                        translations: currentPhrase.translations,\n                        exampleSentences: currentPhrase.exampleSentences,\n                        synonyms: currentPhrase.synonyms\n                    }\n                })\n            });\n            // Fetch a new phrase\n            fetchRandomPhrase();\n        } catch (error) {\n            console.error(\"Error marking phrase as known:\", error);\n            // Fallback to demo mode\n            fetchRandomPhrase();\n        }\n    };\n    const handleDontKnowPhrase = async ()=>{\n        if (!currentPhrase) return;\n        if (status === \"unauthenticated\") {\n            // Demo mode - just show translation\n            setShowTranslation(true);\n            return;\n        }\n        try {\n            await fetch(\"/api/phrases/status\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    phraseId: currentPhrase.id,\n                    status: \"learning\"\n                })\n            });\n            // Show translation\n            setShowTranslation(true);\n        } catch (error) {\n            console.error(\"Error marking phrase as learning:\", error);\n            // Fallback to demo mode\n            setShowTranslation(true);\n        }\n    };\n    const handleLanguageChange = async (newLanguage)=>{\n        try {\n            // Update user preference in database\n            const response = await fetch(\"/api/user/preferences\", {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    preferredLanguage: newLanguage\n                })\n            });\n            if (response.ok) {\n                setSelectedLanguage(newLanguage);\n                setShowLanguageModal(false);\n                // Refresh the page to load phrases in new language\n                window.location.reload();\n            } else {\n                console.error(\"Failed to update language preference\");\n            }\n        } catch (error) {\n            console.error(\"Error updating language preference:\", error);\n        }\n    };\n    const handleLogout = async ()=>{\n        await (0,next_auth_react__WEBPACK_IMPORTED_MODULE_2__.signOut)({\n            callbackUrl: \"/\"\n        });\n    };\n    if (loading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex items-center justify-center min-h-screen\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500\"\n            }, void 0, false, {\n                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                lineNumber: 260,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n            lineNumber: 259,\n            columnNumber: 7\n        }, this);\n    }\n    if (!currentPhrase) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"flex flex-col items-center justify-center min-h-screen p-4\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"w-full max-w-md p-6 bg-white rounded-lg shadow-md text-center\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                        className: \"text-xl font-semibold mb-4\",\n                        children: \"No phrases available\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                        lineNumber: 269,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                        className: \"mb-4\",\n                        children: \"We couldn't find any phrases for you to learn right now.\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                        lineNumber: 270,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                        onClick: ()=>window.location.reload(),\n                        className: \"bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700 transition-colors\",\n                        children: \"Refresh\"\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                        lineNumber: 271,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                lineNumber: 268,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n            lineNumber: 267,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen p-4 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-slate-900 dark:via-slate-800 dark:to-slate-900\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-4xl mx-auto mb-8\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between mb-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                href: \"/\",\n                                className: \"inline-flex items-center gap-2 text-slate-600 hover:text-slate-800 dark:text-slate-200 dark:hover:text-white transition-colors\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-4 h-4\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M15 19l-7-7 7-7\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 292,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 291,\n                                        columnNumber: 13\n                                    }, this),\n                                    \"Home\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            status === \"authenticated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center gap-4\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-sm text-slate-600 dark:text-slate-200\",\n                                        children: [\n                                            \"Welcome back, \",\n                                            session?.user?.name?.split(\" \")[0],\n                                            \"! \\uD83D\\uDC4B\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: ()=>setShowLanguageModal(true),\n                                        className: \"inline-flex items-center gap-2 px-3 py-1.5 text-sm bg-blue-100 hover:bg-blue-200 dark:bg-blue-900/50 dark:hover:bg-blue-800/50 text-blue-700 dark:text-blue-300 rounded-lg transition-colors\",\n                                        title: \"Change language preference\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 310,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 309,\n                                                columnNumber: 17\n                                            }, this),\n                                            session?.user?.preferredLanguage === \"am\" ? \"አማርኛ\" : \"Espa\\xf1ol\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 304,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                        onClick: handleLogout,\n                                        className: \"inline-flex items-center gap-2 px-3 py-1.5 text-sm bg-red-100 hover:bg-red-200 dark:bg-red-900/50 dark:hover:bg-red-800/50 text-red-700 dark:text-red-300 rounded-lg transition-colors\",\n                                        title: \"Sign out\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                className: \"w-4 h-4\",\n                                                fill: \"none\",\n                                                stroke: \"currentColor\",\n                                                viewBox: \"0 0 24 24\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                    strokeLinecap: \"round\",\n                                                    strokeLinejoin: \"round\",\n                                                    strokeWidth: 2,\n                                                    d: \"M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 322,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 321,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"Logout\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 316,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    status === \"unauthenticated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"glass-card p-4 mb-6 animate-slide-up dark:bg-slate-800/80 dark:border-slate-600\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center gap-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-10 h-10 bg-gradient-to-br from-amber-400 to-orange-500 rounded-xl flex items-center justify-center\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-5 h-5 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 335,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 334,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"font-semibold text-slate-800 dark:text-white\",\n                                            children: \"Demo Mode\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 339,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-slate-600 dark:text-slate-200\",\n                                            children: [\n                                                \"You're viewing sample phrases. \",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    href: \"/auth/signin\",\n                                                    className: \"text-blue-600 hover:text-blue-700 dark:text-blue-300 dark:hover:text-blue-200 underline\",\n                                                    children: \"Sign in\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 340,\n                                                    columnNumber: 106\n                                                }, this),\n                                                \" for full functionality.\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 332,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                lineNumber: 285,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col items-center justify-center\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"w-full max-w-2xl card p-8 animate-slide-up dark:bg-slate-800/90 dark:border-slate-600 dark:shadow-2xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"inline-flex items-center justify-center w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl shadow-lg mb-4\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-8 h-8 text-white\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.746 0 3.332.477 4.5 1.253v13C19.832 18.477 18.246 18 16.5 18c-1.746 0-3.332.477-4.5 1.253\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 354,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 352,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                    className: \"text-3xl font-bold text-slate-800 dark:text-white mb-2\",\n                                    children: \"Learn English Phrases\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 357,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-slate-600 dark:text-slate-200\",\n                                    children: \"Master phrases through interactive learning\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 351,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex items-center justify-center gap-3 mb-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center gap-2 bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-200 px-3 py-1 rounded-full text-sm font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-4 h-4\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 366,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 365,\n                                                    columnNumber: 17\n                                                }, this),\n                                                currentPhrase.phraseType.replace(\"_\", \" \").toUpperCase()\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 364,\n                                            columnNumber: 15\n                                        }, this),\n                                        status === \"authenticated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"inline-flex items-center gap-1 bg-green-100 dark:bg-green-800/60 text-green-700 dark:text-green-200 px-2 py-1 rounded-full text-xs font-medium\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-3 h-3\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 13l4 4L19 7\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 373,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 372,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"API\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 363,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-4xl font-bold text-slate-800 dark:text-white mb-4 leading-tight\",\n                                    children: [\n                                        '\"',\n                                        currentPhrase.englishPhrase,\n                                        '\"'\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"w-24 h-1 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full mx-auto\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 382,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 362,\n                            columnNumber: 11\n                        }, this),\n                        !showTranslation ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-6\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"text-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-lg text-slate-700 dark:text-slate-200 mb-6\",\n                                        children: \"Do you know this phrase?\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 388,\n                                        columnNumber: 17\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"grid grid-cols-1 sm:grid-cols-2 gap-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleKnowPhrase,\n                                                className: \"btn-success flex items-center justify-center gap-3 py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M5 13l4 4L19 7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 395,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 394,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"Yes, I know it\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 390,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                onClick: handleDontKnowPhrase,\n                                                className: \"btn-primary flex items-center justify-center gap-3 py-4\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 404,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 403,\n                                                        columnNumber: 21\n                                                    }, this),\n                                                    \"No, show me\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 399,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 17\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 386,\n                            columnNumber: 13\n                        }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-8 animate-fade-in\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card p-6 border-l-4 border-blue-500\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center gap-3 mb-4\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-10 h-10 bg-gradient-to-br from-blue-500 to-blue-600 rounded-xl flex items-center justify-center\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-5 h-5 text-white\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M3 5h12M9 3v2m1.048 9.5A18.022 18.022 0 016.412 9m6.088 9h7M11 21l5-10 5 10M12.751 5C11.783 10.77 8.07 15.61 3 18.129\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 418,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 417,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 416,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"text-xl font-semibold text-slate-800\",\n                                                    children: \"Translation\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 421,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 415,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg text-slate-700 leading-relaxed\",\n                                            children: currentPhrase.translations[session?.user?.preferredLanguage || \"es\"]\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 17\n                                        }, this),\n                                        status === \"unauthenticated\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 p-3 bg-slate-50 rounded-lg\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-sm text-slate-600\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"font-medium\",\n                                                        children: \"Available languages:\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 429,\n                                                        columnNumber: 23\n                                                    }, this),\n                                                    \" Spanish, Chinese, French, German\"\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 428,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 427,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 414,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowExamples(!showExamples),\n                                            className: \"w-full flex justify-between items-center p-6 hover:bg-slate-50 transition-colors group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-emerald-500 to-emerald-600 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-white\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                                    lineNumber: 444,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                                lineNumber: 443,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 442,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-semibold text-slate-800\",\n                                                            children: \"Example Sentences\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 441,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-8 h-8 rounded-full bg-slate-100 flex items-center justify-center transition-transform duration-200 ${showExamples ? \"rotate-180\" : \"\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-slate-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 451,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 450,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 449,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 17\n                                        }, this),\n                                        showExamples && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 pb-6 space-y-4 animate-slide-up\",\n                                            children: currentPhrase.exampleSentences.map((example, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"bg-white/50 border border-slate-200 rounded-xl p-4 hover:bg-white/80 transition-colors\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-800 font-medium mb-2 leading-relaxed\",\n                                                            children: [\n                                                                '\"',\n                                                                example.english,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 460,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-600 text-sm leading-relaxed\",\n                                                            children: example.translated[session?.user?.preferredLanguage || \"es\"]\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 461,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 23\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 457,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 436,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"glass-card overflow-hidden\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setShowSynonyms(!showSynonyms),\n                                            className: \"w-full flex justify-between items-center p-6 hover:bg-slate-50 transition-colors group\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-10 h-10 bg-gradient-to-br from-purple-500 to-purple-600 rounded-xl flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                                className: \"w-5 h-5 text-white\",\n                                                                fill: \"none\",\n                                                                stroke: \"currentColor\",\n                                                                viewBox: \"0 0 24 24\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                                    strokeLinecap: \"round\",\n                                                                    strokeLinejoin: \"round\",\n                                                                    strokeWidth: 2,\n                                                                    d: \"M8 16H6a2 2 0 01-2-2V6a2 2 0 012-2h8a2 2 0 012 2v2m-6 12h8a2 2 0 002-2v-8a2 2 0 00-2-2h-8a2 2 0 00-2 2v8a2 2 0 002 2z\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                                    lineNumber: 479,\n                                                                    columnNumber: 25\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                                lineNumber: 478,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 477,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-lg font-semibold text-slate-800\",\n                                                            children: \"Similar Phrases\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 482,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 476,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: `w-8 h-8 rounded-full bg-slate-100 flex items-center justify-center transition-transform duration-200 ${showSynonyms ? \"rotate-180\" : \"\"}`,\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                        className: \"w-4 h-4 text-slate-600\",\n                                                        fill: \"none\",\n                                                        stroke: \"currentColor\",\n                                                        viewBox: \"0 0 24 24\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                            strokeLinecap: \"round\",\n                                                            strokeLinejoin: \"round\",\n                                                            strokeWidth: 2,\n                                                            d: \"M19 9l-7 7-7-7\"\n                                                        }, void 0, false, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 486,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 485,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 484,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 472,\n                                            columnNumber: 17\n                                        }, this),\n                                        showSynonyms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"px-6 pb-6 animate-slide-up\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-1 gap-3\",\n                                                children: currentPhrase.synonyms.map((synonym, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-white/50 border border-slate-200 rounded-xl p-4 hover:bg-white/80 transition-colors\",\n                                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                            className: \"text-slate-700 font-medium\",\n                                                            children: [\n                                                                '\"',\n                                                                synonym,\n                                                                '\"'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                            lineNumber: 496,\n                                                            columnNumber: 27\n                                                        }, this)\n                                                    }, index, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 493,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 492,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 471,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col sm:flex-row gap-4 pt-6 border-t border-slate-200 dark:border-slate-600\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>fetchRandomPhrase(),\n                                            className: \"btn-secondary flex items-center justify-center gap-3 flex-1\",\n                                            title: \"Skip this phrase and get a new one\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 512,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 511,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Next Phrase\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 506,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: async ()=>{\n                                                if (!currentPhrase) return;\n                                                if (status === \"unauthenticated\") {\n                                                    fetchRandomPhrase();\n                                                    return;\n                                                }\n                                                try {\n                                                    await fetch(\"/api/phrases/status\", {\n                                                        method: \"POST\",\n                                                        headers: {\n                                                            \"Content-Type\": \"application/json\"\n                                                        },\n                                                        body: JSON.stringify({\n                                                            phraseId: currentPhrase.id,\n                                                            status: \"learning\",\n                                                            phraseData: {\n                                                                englishPhrase: currentPhrase.englishPhrase,\n                                                                phraseType: currentPhrase.phraseType,\n                                                                translations: currentPhrase.translations,\n                                                                exampleSentences: currentPhrase.exampleSentences,\n                                                                synonyms: currentPhrase.synonyms\n                                                            }\n                                                        })\n                                                    });\n                                                    fetchRandomPhrase();\n                                                } catch (error) {\n                                                    console.error(\"Error marking for review:\", error);\n                                                    fetchRandomPhrase();\n                                                }\n                                            },\n                                            className: \"btn-primary flex items-center justify-center gap-3 flex-1\",\n                                            title: \"Save this phrase to review later - it will appear again in future sessions\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                                    className: \"w-5 h-5\",\n                                                    fill: \"none\",\n                                                    stroke: \"currentColor\",\n                                                    viewBox: \"0 0 24 24\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                        strokeLinecap: \"round\",\n                                                        strokeLinejoin: \"round\",\n                                                        strokeWidth: 2,\n                                                        d: \"M5 5a2 2 0 012-2h10a2 2 0 012 2v16l-7-3.5L5 21V5z\"\n                                                    }, void 0, false, {\n                                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                        lineNumber: 555,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 554,\n                                                    columnNumber: 19\n                                                }, this),\n                                                \"Save for Later\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 517,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 505,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"mt-4 p-3 bg-blue-50 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-sm text-blue-700 dark:text-blue-200\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"strong\", {\n                                                children: \"\\uD83D\\uDCA1 How it works:\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 564,\n                                                columnNumber: 19\n                                            }, this),\n                                            ' \"Yes, I know it\" marks the phrase as learned (won\\'t appear again). \"Save for Later\" keeps it in your review queue for future practice.'\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 563,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 562,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                    lineNumber: 349,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            showLanguageModal && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"bg-white dark:bg-slate-800 rounded-lg p-6 w-full max-w-md mx-4 shadow-xl\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"flex items-center justify-between mb-4\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                    className: \"text-lg font-semibold text-slate-800 dark:text-white\",\n                                    children: \"Choose Your Language\"\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 578,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>setShowLanguageModal(false),\n                                    className: \"text-slate-400 hover:text-slate-600 dark:text-slate-300 dark:hover:text-slate-100\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                        className: \"w-6 h-6\",\n                                        fill: \"none\",\n                                        stroke: \"currentColor\",\n                                        viewBox: \"0 0 24 24\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                            strokeLinecap: \"round\",\n                                            strokeLinejoin: \"round\",\n                                            strokeWidth: 2,\n                                            d: \"M6 18L18 6M6 6l12 12\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 586,\n                                            columnNumber: 19\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                        lineNumber: 585,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 581,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 577,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-600 dark:text-slate-300 mb-6\",\n                            children: \"Select your preferred language for learning English phrases:\"\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 591,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"space-y-3\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleLanguageChange(\"am\"),\n                                    className: `w-full flex items-center gap-3 p-4 rounded-lg border-2 transition-colors ${selectedLanguage === \"am\" ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300\" : \"border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500 text-slate-700 dark:text-slate-300\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83C\\uDDEA\\uD83C\\uDDF9\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 604,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold\",\n                                                    children: \"አማርኛ (Amharic)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 606,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm opacity-75\",\n                                                    children: \"Learn English phrases in Amharic\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 607,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 605,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedLanguage === \"am\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 ml-auto text-blue-500\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M5 13l4 4L19 7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 611,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 610,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 596,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: ()=>handleLanguageChange(\"es\"),\n                                    className: `w-full flex items-center gap-3 p-4 rounded-lg border-2 transition-colors ${selectedLanguage === \"es\" ? \"border-blue-500 bg-blue-50 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300\" : \"border-slate-200 dark:border-slate-600 hover:border-slate-300 dark:hover:border-slate-500 text-slate-700 dark:text-slate-300\"}`,\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-2xl\",\n                                            children: \"\\uD83C\\uDDEA\\uD83C\\uDDF8\"\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 624,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"text-left\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"font-semibold\",\n                                                    children: \"Espa\\xf1ol (Spanish)\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 626,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"text-sm opacity-75\",\n                                                    children: \"Learn English phrases in Spanish\"\n                                                }, void 0, false, {\n                                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                    lineNumber: 627,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 625,\n                                            columnNumber: 17\n                                        }, this),\n                                        selectedLanguage === \"es\" && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"svg\", {\n                                            className: \"w-5 h-5 ml-auto text-blue-500\",\n                                            fill: \"none\",\n                                            stroke: \"currentColor\",\n                                            viewBox: \"0 0 24 24\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"path\", {\n                                                strokeLinecap: \"round\",\n                                                strokeLinejoin: \"round\",\n                                                strokeWidth: 2,\n                                                d: \"M5 13l4 4L19 7\"\n                                            }, void 0, false, {\n                                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                                lineNumber: 631,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                            lineNumber: 630,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                    lineNumber: 616,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 595,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"mt-6 flex gap-3\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                onClick: ()=>setShowLanguageModal(false),\n                                className: \"flex-1 px-4 py-2 text-slate-600 dark:text-slate-300 border border-slate-300 dark:border-slate-600 rounded-lg hover:bg-slate-50 dark:hover:bg-slate-700 transition-colors\",\n                                children: \"Cancel\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                                lineNumber: 638,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                            lineNumber: 637,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                    lineNumber: 576,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n                lineNumber: 575,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\learn\\\\page.tsx\",\n        lineNumber: 283,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./app/learn/page.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"583937383094\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9sYW5ndWFnZS8uL2FwcC9nbG9iYWxzLmNzcz9lNjAxIl0sInNvdXJjZXNDb250ZW50IjpbImV4cG9ydCBkZWZhdWx0IFwiNTgzOTM3MzgzMDk0XCJcbmlmIChtb2R1bGUuaG90KSB7IG1vZHVsZS5ob3QuYWNjZXB0KCkgfVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/api/auth/[...nextauth]/route.ts":
/*!*********************************************!*\
  !*** ./app/api/auth/[...nextauth]/route.ts ***!
  \*********************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GET: () => (/* binding */ handler),\n/* harmony export */   POST: () => (/* binding */ handler),\n/* harmony export */   authOptions: () => (/* binding */ authOptions)\n/* harmony export */ });\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next-auth/providers/google */ \"(rsc)/./node_modules/next-auth/providers/google.js\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @prisma/client */ \"@prisma/client\");\n/* harmony import */ var _prisma_client__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_prisma_client__WEBPACK_IMPORTED_MODULE_2__);\n\n\n\nconst authOptions = {\n    // Temporarily disable database adapter to test OAuth flow\n    // adapter: PrismaAdapter(prisma),\n    providers: [\n        (0,next_auth_providers_google__WEBPACK_IMPORTED_MODULE_1__[\"default\"])({\n            clientId: process.env.GOOGLE_CLIENT_ID,\n            clientSecret: process.env.GOOGLE_CLIENT_SECRET\n        })\n    ],\n    callbacks: {\n        async session ({ session, token }) {\n            if (session.user && token?.sub) {\n                session.user.id = token.sub;\n                // Include preferred language from token\n                session.user.preferredLanguage = token.preferredLanguage;\n            }\n            return session;\n        },\n        async jwt ({ token, account, profile, trigger, session }) {\n            if (account) {\n                token.accessToken = account.access_token;\n                // When user first signs in, fetch their language preference from database\n                try {\n                    const prismaClient = new _prisma_client__WEBPACK_IMPORTED_MODULE_2__.PrismaClient();\n                    let user = await prismaClient.user.findUnique({\n                        where: {\n                            id: token.sub\n                        },\n                        select: {\n                            preferredLanguage: true\n                        }\n                    });\n                    // If user doesn't exist, create them with default language preference\n                    if (!user) {\n                        user = await prismaClient.user.create({\n                            data: {\n                                id: token.sub,\n                                name: token.name,\n                                email: token.email,\n                                image: token.picture,\n                                preferredLanguage: \"am\" // Default to Amharic\n                            },\n                            select: {\n                                preferredLanguage: true\n                            }\n                        });\n                        console.log(`Created new user with default language preference: ${token.sub}`);\n                    }\n                    token.preferredLanguage = user.preferredLanguage;\n                    console.log(`Loaded language preference for user ${token.sub}: ${user.preferredLanguage}`);\n                    await prismaClient.$disconnect();\n                } catch (error) {\n                    console.error(\"Error fetching user language preference:\", error);\n                    token.preferredLanguage = \"am\"; // Default fallback\n                }\n            }\n            // Handle session updates (like language preference changes)\n            if (trigger === \"update\" && session?.user?.preferredLanguage) {\n                token.preferredLanguage = session.user.preferredLanguage;\n            }\n            return token;\n        }\n    },\n    pages: {\n        signIn: \"/auth/signin\"\n    }\n};\nconst handler = next_auth__WEBPACK_IMPORTED_MODULE_0___default()(authOptions);\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvYXBpL2F1dGgvWy4uLm5leHRhdXRoXS9yb3V0ZS50cyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7OztBQUFpQztBQUN1QjtBQUdWO0FBRXZDLE1BQU1HLGNBQWM7SUFDekIsMERBQTBEO0lBQzFELGtDQUFrQztJQUNsQ0MsV0FBVztRQUNUSCxzRUFBY0EsQ0FBQztZQUNiSSxVQUFVQyxRQUFRQyxHQUFHLENBQUNDLGdCQUFnQjtZQUN0Q0MsY0FBY0gsUUFBUUMsR0FBRyxDQUFDRyxvQkFBb0I7UUFDaEQ7S0FDRDtJQUNEQyxXQUFXO1FBQ1QsTUFBTUMsU0FBUSxFQUFFQSxPQUFPLEVBQUVDLEtBQUssRUFBTztZQUNuQyxJQUFJRCxRQUFRRSxJQUFJLElBQUlELE9BQU9FLEtBQUs7Z0JBQzlCSCxRQUFRRSxJQUFJLENBQUNFLEVBQUUsR0FBR0gsTUFBTUUsR0FBRztnQkFDM0Isd0NBQXdDO2dCQUN4Q0gsUUFBUUUsSUFBSSxDQUFDRyxpQkFBaUIsR0FBR0osTUFBTUksaUJBQWlCO1lBQzFEO1lBQ0EsT0FBT0w7UUFDVDtRQUNBLE1BQU1NLEtBQUksRUFBRUwsS0FBSyxFQUFFTSxPQUFPLEVBQUVDLE9BQU8sRUFBRUMsT0FBTyxFQUFFVCxPQUFPLEVBQU87WUFDMUQsSUFBSU8sU0FBUztnQkFDWE4sTUFBTVMsV0FBVyxHQUFHSCxRQUFRSSxZQUFZO2dCQUV4QywwRUFBMEU7Z0JBQzFFLElBQUk7b0JBQ0YsTUFBTUMsZUFBZSxJQUFJdEIsd0RBQVlBO29CQUNyQyxJQUFJWSxPQUFPLE1BQU1VLGFBQWFWLElBQUksQ0FBQ1csVUFBVSxDQUFDO3dCQUM1Q0MsT0FBTzs0QkFBRVYsSUFBSUgsTUFBTUUsR0FBRzt3QkFBQzt3QkFDdkJZLFFBQVE7NEJBQUVWLG1CQUFtQjt3QkFBSztvQkFDcEM7b0JBRUEsc0VBQXNFO29CQUN0RSxJQUFJLENBQUNILE1BQU07d0JBQ1RBLE9BQU8sTUFBTVUsYUFBYVYsSUFBSSxDQUFDYyxNQUFNLENBQUM7NEJBQ3BDQyxNQUFNO2dDQUNKYixJQUFJSCxNQUFNRSxHQUFHO2dDQUNiZSxNQUFNakIsTUFBTWlCLElBQUk7Z0NBQ2hCQyxPQUFPbEIsTUFBTWtCLEtBQUs7Z0NBQ2xCQyxPQUFPbkIsTUFBTW9CLE9BQU87Z0NBQ3BCaEIsbUJBQW1CLEtBQUsscUJBQXFCOzRCQUMvQzs0QkFDQVUsUUFBUTtnQ0FBRVYsbUJBQW1COzRCQUFLO3dCQUNwQzt3QkFDQWlCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG1EQUFtRCxFQUFFdEIsTUFBTUUsR0FBRyxDQUFDLENBQUM7b0JBQy9FO29CQUVBRixNQUFNSSxpQkFBaUIsR0FBR0gsS0FBS0csaUJBQWlCO29CQUNoRGlCLFFBQVFDLEdBQUcsQ0FBQyxDQUFDLG9DQUFvQyxFQUFFdEIsTUFBTUUsR0FBRyxDQUFDLEVBQUUsRUFBRUQsS0FBS0csaUJBQWlCLENBQUMsQ0FBQztvQkFDekYsTUFBTU8sYUFBYVksV0FBVztnQkFDaEMsRUFBRSxPQUFPQyxPQUFPO29CQUNkSCxRQUFRRyxLQUFLLENBQUMsNENBQTRDQTtvQkFDMUR4QixNQUFNSSxpQkFBaUIsR0FBRyxNQUFNLG1CQUFtQjtnQkFDckQ7WUFDRjtZQUVBLDREQUE0RDtZQUM1RCxJQUFJSSxZQUFZLFlBQVlULFNBQVNFLE1BQU1HLG1CQUFtQjtnQkFDNURKLE1BQU1JLGlCQUFpQixHQUFHTCxRQUFRRSxJQUFJLENBQUNHLGlCQUFpQjtZQUMxRDtZQUVBLE9BQU9KO1FBQ1Q7SUFDRjtJQUNBeUIsT0FBTztRQUNMQyxRQUFRO0lBQ1Y7QUFDRixFQUFFO0FBRUYsTUFBTUMsVUFBVXhDLGdEQUFRQSxDQUFDRztBQUNrQiIsInNvdXJjZXMiOlsid2VicGFjazovL2xhbmd1YWdlLy4vYXBwL2FwaS9hdXRoL1suLi5uZXh0YXV0aF0vcm91dGUudHM/YzhhNCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgTmV4dEF1dGggZnJvbSBcIm5leHQtYXV0aFwiO1xuaW1wb3J0IEdvb2dsZVByb3ZpZGVyIGZyb20gXCJuZXh0LWF1dGgvcHJvdmlkZXJzL2dvb2dsZVwiO1xuaW1wb3J0IHsgUHJpc21hQWRhcHRlciB9IGZyb20gXCJAYXV0aC9wcmlzbWEtYWRhcHRlclwiO1xuaW1wb3J0IHsgcHJpc21hIH0gZnJvbSBcIkAvbGliL3ByaXNtYVwiO1xuaW1wb3J0IHsgUHJpc21hQ2xpZW50IH0gZnJvbSBcIkBwcmlzbWEvY2xpZW50XCI7XG5cbmV4cG9ydCBjb25zdCBhdXRoT3B0aW9ucyA9IHtcbiAgLy8gVGVtcG9yYXJpbHkgZGlzYWJsZSBkYXRhYmFzZSBhZGFwdGVyIHRvIHRlc3QgT0F1dGggZmxvd1xuICAvLyBhZGFwdGVyOiBQcmlzbWFBZGFwdGVyKHByaXNtYSksXG4gIHByb3ZpZGVyczogW1xuICAgIEdvb2dsZVByb3ZpZGVyKHtcbiAgICAgIGNsaWVudElkOiBwcm9jZXNzLmVudi5HT09HTEVfQ0xJRU5UX0lEISxcbiAgICAgIGNsaWVudFNlY3JldDogcHJvY2Vzcy5lbnYuR09PR0xFX0NMSUVOVF9TRUNSRVQhLFxuICAgIH0pLFxuICBdLFxuICBjYWxsYmFja3M6IHtcbiAgICBhc3luYyBzZXNzaW9uKHsgc2Vzc2lvbiwgdG9rZW4gfTogYW55KSB7XG4gICAgICBpZiAoc2Vzc2lvbi51c2VyICYmIHRva2VuPy5zdWIpIHtcbiAgICAgICAgc2Vzc2lvbi51c2VyLmlkID0gdG9rZW4uc3ViO1xuICAgICAgICAvLyBJbmNsdWRlIHByZWZlcnJlZCBsYW5ndWFnZSBmcm9tIHRva2VuXG4gICAgICAgIHNlc3Npb24udXNlci5wcmVmZXJyZWRMYW5ndWFnZSA9IHRva2VuLnByZWZlcnJlZExhbmd1YWdlO1xuICAgICAgfVxuICAgICAgcmV0dXJuIHNlc3Npb247XG4gICAgfSxcbiAgICBhc3luYyBqd3QoeyB0b2tlbiwgYWNjb3VudCwgcHJvZmlsZSwgdHJpZ2dlciwgc2Vzc2lvbiB9OiBhbnkpIHtcbiAgICAgIGlmIChhY2NvdW50KSB7XG4gICAgICAgIHRva2VuLmFjY2Vzc1Rva2VuID0gYWNjb3VudC5hY2Nlc3NfdG9rZW47XG5cbiAgICAgICAgLy8gV2hlbiB1c2VyIGZpcnN0IHNpZ25zIGluLCBmZXRjaCB0aGVpciBsYW5ndWFnZSBwcmVmZXJlbmNlIGZyb20gZGF0YWJhc2VcbiAgICAgICAgdHJ5IHtcbiAgICAgICAgICBjb25zdCBwcmlzbWFDbGllbnQgPSBuZXcgUHJpc21hQ2xpZW50KCk7XG4gICAgICAgICAgbGV0IHVzZXIgPSBhd2FpdCBwcmlzbWFDbGllbnQudXNlci5maW5kVW5pcXVlKHtcbiAgICAgICAgICAgIHdoZXJlOiB7IGlkOiB0b2tlbi5zdWIgfSxcbiAgICAgICAgICAgIHNlbGVjdDogeyBwcmVmZXJyZWRMYW5ndWFnZTogdHJ1ZSB9XG4gICAgICAgICAgfSk7XG5cbiAgICAgICAgICAvLyBJZiB1c2VyIGRvZXNuJ3QgZXhpc3QsIGNyZWF0ZSB0aGVtIHdpdGggZGVmYXVsdCBsYW5ndWFnZSBwcmVmZXJlbmNlXG4gICAgICAgICAgaWYgKCF1c2VyKSB7XG4gICAgICAgICAgICB1c2VyID0gYXdhaXQgcHJpc21hQ2xpZW50LnVzZXIuY3JlYXRlKHtcbiAgICAgICAgICAgICAgZGF0YToge1xuICAgICAgICAgICAgICAgIGlkOiB0b2tlbi5zdWIsXG4gICAgICAgICAgICAgICAgbmFtZTogdG9rZW4ubmFtZSxcbiAgICAgICAgICAgICAgICBlbWFpbDogdG9rZW4uZW1haWwsXG4gICAgICAgICAgICAgICAgaW1hZ2U6IHRva2VuLnBpY3R1cmUsXG4gICAgICAgICAgICAgICAgcHJlZmVycmVkTGFuZ3VhZ2U6ICdhbScgLy8gRGVmYXVsdCB0byBBbWhhcmljXG4gICAgICAgICAgICAgIH0sXG4gICAgICAgICAgICAgIHNlbGVjdDogeyBwcmVmZXJyZWRMYW5ndWFnZTogdHJ1ZSB9XG4gICAgICAgICAgICB9KTtcbiAgICAgICAgICAgIGNvbnNvbGUubG9nKGBDcmVhdGVkIG5ldyB1c2VyIHdpdGggZGVmYXVsdCBsYW5ndWFnZSBwcmVmZXJlbmNlOiAke3Rva2VuLnN1Yn1gKTtcbiAgICAgICAgICB9XG5cbiAgICAgICAgICB0b2tlbi5wcmVmZXJyZWRMYW5ndWFnZSA9IHVzZXIucHJlZmVycmVkTGFuZ3VhZ2U7XG4gICAgICAgICAgY29uc29sZS5sb2coYExvYWRlZCBsYW5ndWFnZSBwcmVmZXJlbmNlIGZvciB1c2VyICR7dG9rZW4uc3VifTogJHt1c2VyLnByZWZlcnJlZExhbmd1YWdlfWApO1xuICAgICAgICAgIGF3YWl0IHByaXNtYUNsaWVudC4kZGlzY29ubmVjdCgpO1xuICAgICAgICB9IGNhdGNoIChlcnJvcikge1xuICAgICAgICAgIGNvbnNvbGUuZXJyb3IoJ0Vycm9yIGZldGNoaW5nIHVzZXIgbGFuZ3VhZ2UgcHJlZmVyZW5jZTonLCBlcnJvcik7XG4gICAgICAgICAgdG9rZW4ucHJlZmVycmVkTGFuZ3VhZ2UgPSAnYW0nOyAvLyBEZWZhdWx0IGZhbGxiYWNrXG4gICAgICAgIH1cbiAgICAgIH1cblxuICAgICAgLy8gSGFuZGxlIHNlc3Npb24gdXBkYXRlcyAobGlrZSBsYW5ndWFnZSBwcmVmZXJlbmNlIGNoYW5nZXMpXG4gICAgICBpZiAodHJpZ2dlciA9PT0gXCJ1cGRhdGVcIiAmJiBzZXNzaW9uPy51c2VyPy5wcmVmZXJyZWRMYW5ndWFnZSkge1xuICAgICAgICB0b2tlbi5wcmVmZXJyZWRMYW5ndWFnZSA9IHNlc3Npb24udXNlci5wcmVmZXJyZWRMYW5ndWFnZTtcbiAgICAgIH1cblxuICAgICAgcmV0dXJuIHRva2VuO1xuICAgIH0sXG4gIH0sXG4gIHBhZ2VzOiB7XG4gICAgc2lnbkluOiBcIi9hdXRoL3NpZ25pblwiLFxuICB9LFxufTtcblxuY29uc3QgaGFuZGxlciA9IE5leHRBdXRoKGF1dGhPcHRpb25zKTtcbmV4cG9ydCB7IGhhbmRsZXIgYXMgR0VULCBoYW5kbGVyIGFzIFBPU1QgfTsiXSwibmFtZXMiOlsiTmV4dEF1dGgiLCJHb29nbGVQcm92aWRlciIsIlByaXNtYUNsaWVudCIsImF1dGhPcHRpb25zIiwicHJvdmlkZXJzIiwiY2xpZW50SWQiLCJwcm9jZXNzIiwiZW52IiwiR09PR0xFX0NMSUVOVF9JRCIsImNsaWVudFNlY3JldCIsIkdPT0dMRV9DTElFTlRfU0VDUkVUIiwiY2FsbGJhY2tzIiwic2Vzc2lvbiIsInRva2VuIiwidXNlciIsInN1YiIsImlkIiwicHJlZmVycmVkTGFuZ3VhZ2UiLCJqd3QiLCJhY2NvdW50IiwicHJvZmlsZSIsInRyaWdnZXIiLCJhY2Nlc3NUb2tlbiIsImFjY2Vzc190b2tlbiIsInByaXNtYUNsaWVudCIsImZpbmRVbmlxdWUiLCJ3aGVyZSIsInNlbGVjdCIsImNyZWF0ZSIsImRhdGEiLCJuYW1lIiwiZW1haWwiLCJpbWFnZSIsInBpY3R1cmUiLCJjb25zb2xlIiwibG9nIiwiJGRpc2Nvbm5lY3QiLCJlcnJvciIsInBhZ2VzIiwic2lnbkluIiwiaGFuZGxlciIsIkdFVCIsIlBPU1QiXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./app/api/auth/[...nextauth]/route.ts\n");

/***/ }),

/***/ "(rsc)/./app/components/AuthProvider.tsx":
/*!*****************************************!*\
  !*** ./app/components/AuthProvider.tsx ***!
  \*****************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Software Projects\Language\app\components\AuthProvider.tsx#default`));


/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"]}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"]}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-auth */ \"(rsc)/./node_modules/next-auth/index.js\");\n/* harmony import */ var next_auth__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_auth__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./api/auth/[...nextauth]/route */ \"(rsc)/./app/api/auth/[...nextauth]/route.ts\");\n/* harmony import */ var _components_AuthProvider__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./components/AuthProvider */ \"(rsc)/./app/components/AuthProvider.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"English Phrase Pro\",\n    description: \"Learn English phrases with translations and examples\"\n};\nasync function RootLayout({ children }) {\n    const session = await (0,next_auth__WEBPACK_IMPORTED_MODULE_2__.getServerSession)(_api_auth_nextauth_route__WEBPACK_IMPORTED_MODULE_3__.authOptions);\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        className: \"scroll-smooth\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_variableName_inter___WEBPACK_IMPORTED_MODULE_5___default().className)} antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_AuthProvider__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                session: session,\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"fixed inset-0 -z-10\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute inset-0 bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-100\"\n                            }, void 0, false, {\n                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"absolute top-0 left-0 w-full h-full opacity-30\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-20 left-20 w-72 h-72 bg-blue-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse-slow\"\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n                                        lineNumber: 29,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute top-40 right-20 w-72 h-72 bg-purple-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse-slow\",\n                                        style: {\n                                            animationDelay: \"2s\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n                                        lineNumber: 30,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"absolute bottom-20 left-40 w-72 h-72 bg-pink-300 rounded-full mix-blend-multiply filter blur-xl animate-pulse-slow\",\n                                        style: {\n                                            animationDelay: \"4s\"\n                                        }\n                                    }, void 0, false, {\n                                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n                                lineNumber: 28,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"main\", {\n                        className: \"relative min-h-screen\",\n                        children: children\n                    }, void 0, false, {\n                        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n                        lineNumber: 35,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n                lineNumber: 24,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n            lineNumber: 23,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"D:\\\\Software Projects\\\\Language\\\\app\\\\layout.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/learn/page.tsx":
/*!****************************!*\
  !*** ./app/learn/page.tsx ***!
  \****************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   "default": () => (__WEBPACK_DEFAULT_EXPORT__)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");

/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`D:\Software Projects\Language\app\learn\page.tsx#default`));


/***/ })

};
;

// load runtime
var __webpack_require__ = require("../../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/next-auth","vendor-chunks/openid-client","vendor-chunks/@babel","vendor-chunks/oauth","vendor-chunks/object-hash","vendor-chunks/preact","vendor-chunks/uuid","vendor-chunks/yallist","vendor-chunks/lru-cache","vendor-chunks/preact-render-to-string","vendor-chunks/oidc-token-hash","vendor-chunks/@panva","vendor-chunks/@swc"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Flearn%2Fpage&page=%2Flearn%2Fpage&appPaths=%2Flearn%2Fpage&pagePath=private-next-app-dir%2Flearn%2Fpage.tsx&appDir=D%3A%5CSoftware%20Projects%5CLanguage%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=D%3A%5CSoftware%20Projects%5CLanguage&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();